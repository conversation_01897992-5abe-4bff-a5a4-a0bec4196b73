#!/usr/bin/env python3
"""
Simple test script for the enhanced bridge sensor code
"""

import os
import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt

def test_basic_functionality():
    """Test basic functionality"""
    print("Testing enhanced bridge sensor code...")
    
    # Test data loading
    base_path = "/Users/<USER>/Documents/bridge_sensor/data/"
    
    # Check if directories exist
    healthy_path = os.path.join(base_path, "healthy")
    unhealthy_path = os.path.join(base_path, "unhealthy")
    
    print(f"Healthy data path exists: {os.path.exists(healthy_path)}")
    print(f"Unhealthy data path exists: {os.path.exists(unhealthy_path)}")
    
    if os.path.exists(healthy_path):
        healthy_files = [f for f in os.listdir(healthy_path) if f.endswith('.xlsx')]
        print(f"Found {len(healthy_files)} healthy files")
        
        if healthy_files:
            # Test loading one file
            test_file = os.path.join(healthy_path, healthy_files[0])
            try:
                df = pd.read_excel(test_file, usecols=range(6))
                print(f"Successfully loaded test file: {df.shape}")
                
                # Test basic feature extraction
                print("Testing basic feature extraction...")
                
                # Simple RMS calculation
                rms = np.sqrt(np.mean(df.iloc[:1000, 0]**2))
                print(f"Sample RMS: {rms:.4f}")
                
                # Test plotting
                print("Testing plotting...")
                plt.figure(figsize=(8, 6))
                plt.plot(df.iloc[:1000, 0])
                plt.title("Sample Signal")
                plt.xlabel("Sample")
                plt.ylabel("Amplitude")
                
                os.makedirs("img", exist_ok=True)
                plt.savefig("img/test_plot.png", dpi=300, bbox_inches='tight')
                plt.close()
                print("Test plot saved to img/test_plot.png")
                
                print("Basic functionality test PASSED!")
                return True
                
            except Exception as e:
                print(f"Error loading test file: {e}")
                return False
    
    print("Basic functionality test FAILED!")
    return False

if __name__ == "__main__":
    test_basic_functionality()
