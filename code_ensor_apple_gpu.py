#!/usr/bin/env python3
"""
Apple Silicon GPU-Accelerated Bridge Structural Health Monitoring System
Optimized for Mac M1/M2/M3 with MLX and Metal Performance Shaders
"""

import os
import pandas as pd
import numpy as np
import time
from datetime import datetime
import json
import warnings

# Apple GPU acceleration imports
try:
    import mlx.core as mx
    import mlx.nn as nn
    import mlx.optimizers as optim
    MLX_AVAILABLE = True
    print("🍎 MLX (Apple GPU) acceleration available!")
except ImportError:
    MLX_AVAILABLE = False
    print("⚠️ MLX not available, install with: pip install mlx")

# PyTorch with MPS (Metal Performance Shaders) support
try:
    import torch
    if torch.backends.mps.is_available():
        DEVICE = torch.device("mps")
        MPS_AVAILABLE = True
        print("🔥 PyTorch MPS (Apple GPU) acceleration available!")
    else:
        DEVICE = torch.device("cpu")
        MPS_AVAILABLE = False
        print("⚠️ MPS not available, using CPU")
except ImportError:
    MPS_AVAILABLE = False
    DEVICE = "cpu"
    print("⚠️ PyTorch not available")

# Core libraries
from scipy.fft import fft
from scipy.stats import kurtosis, skew, entropy, ttest_ind
from scipy.signal import welch, spectrogram, butter, filtfilt, find_peaks
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.preprocessing import RobustScaler
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import confusion_matrix, roc_curve, auc, accuracy_score, precision_score, recall_score, f1_score
from sklearn.feature_selection import SelectKBest, mutual_info_classif
import xgboost as xgb

import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import seaborn as sns
import pywt

warnings.filterwarnings("ignore")

# Apple GPU optimized parameters
BATCH_SIZE = 2000 if (MLX_AVAILABLE or MPS_AVAILABLE) else 500
MAX_SAMPLES = None  # Use all available files
APPLE_OPTIMIZED = True

print(f"🍎 Apple Silicon Optimization: {APPLE_OPTIMIZED}")
print(f"📱 MLX Available: {MLX_AVAILABLE}")
print(f"🔥 MPS Available: {MPS_AVAILABLE}")
print(f"⚡ Batch Size: {BATCH_SIZE}")

class AppleMLXClassifier:
    """MLX-based neural network classifier for Apple Silicon"""

    def __init__(self, input_size, hidden_size=64, num_classes=2):
        if not MLX_AVAILABLE:
            raise ImportError("MLX not available")

        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_classes = num_classes

        # Define MLX neural network
        self.layers = [
            nn.Linear(input_size, hidden_size),
            nn.ReLU(),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Linear(hidden_size // 2, num_classes)
        ]

        # Initialize parameters
        mx.eval([layer.parameters() for layer in self.layers if hasattr(layer, 'parameters')])

    def forward(self, x):
        for layer in self.layers:
            x = layer(x)
        return x

    def fit(self, X, y, epochs=100, lr=0.001):
        # Convert to MLX arrays
        X_mx = mx.array(X.astype(np.float32))
        y_mx = mx.array(y.astype(np.int32))

        optimizer = optim.Adam(learning_rate=lr)

        def loss_fn(params, X, y):
            # Forward pass
            logits = self.forward(X)
            # Cross-entropy loss
            return mx.mean(nn.losses.cross_entropy(logits, y))

        # Training loop (simplified)
        for epoch in range(epochs):
            loss, grads = mx.value_and_grad(loss_fn)(
                [layer.parameters() for layer in self.layers if hasattr(layer, 'parameters')],
                X_mx, y_mx
            )
            optimizer.update([layer.parameters() for layer in self.layers if hasattr(layer, 'parameters')], grads)

            if epoch % 20 == 0:
                print(f"Epoch {epoch}, Loss: {loss.item():.4f}")

    def predict(self, X):
        X_mx = mx.array(X.astype(np.float32))
        logits = self.forward(X_mx)
        return mx.argmax(logits, axis=1).tolist()

    def predict_proba(self, X):
        X_mx = mx.array(X.astype(np.float32))
        logits = self.forward(X_mx)
        probs = nn.softmax(logits)
        return np.array(probs.tolist())

def load_sensor_data_apple(base_path, condition, max_files=MAX_SAMPLES):
    """Apple Silicon optimized data loading"""
    data_list = []
    folder_path = os.path.join(base_path, condition)

    if max_files is None:
        print(f"\n🍎 Loading ALL {condition} data files...")
    else:
        print(f"\n🍎 Loading {condition} data (max {max_files} files)...")

    if not os.path.exists(folder_path):
        print(f"❌ Error: Directory {folder_path} does not exist.")
        return data_list

    file_count = 0
    for root, _, files in os.walk(folder_path):
        for file in files:
            if file.endswith('.xlsx'):
                if max_files is None or file_count < max_files:
                    file_path = os.path.join(root, file)
                    try:
                        df = pd.read_excel(file_path, usecols=range(6))
                        print(f"✅ Loaded {file}: Shape {df.shape}")
                        data_list.append(df)
                        file_count += 1
                    except Exception as e:
                        print(f"❌ Error loading {file}: {str(e)}")
                else:
                    break

    print(f"📊 Total {condition} files loaded: {len(data_list)}")
    return data_list

def extract_features_apple_optimized(df, sampling_freq=1651, start_sec=2, duration_sec=12, window_sec=1.0):
    """Apple Silicon GPU-optimized feature extraction"""
    start_idx = start_sec * sampling_freq
    end_idx = start_idx + (duration_sec * sampling_freq)

    if start_idx >= len(df):
        return pd.DataFrame()
    if end_idx > len(df):
        end_idx = len(df)

    df_chunk = df.iloc[start_idx:end_idx]
    window_size = int(window_sec * sampling_freq)
    n_windows = len(df_chunk) // window_size

    if n_windows == 0:
        return pd.DataFrame()

    features_list = []

    for sensor in range(6):
        sensor_data = df_chunk.iloc[:, sensor].values
        sensor_features = []

        # Apple Silicon optimized batch processing
        for i in range(0, n_windows, BATCH_SIZE):
            batch_end = min(i + BATCH_SIZE, n_windows)
            batch_features = []

            for j in range(i, batch_end):
                chunk = sensor_data[j*window_size:(j+1)*window_size]

                # Ensure chunk is float32 for MPS compatibility
                chunk = chunk.astype(np.float32)

                # Core statistical features (optimized for Apple Silicon)
                if MPS_AVAILABLE:
                    # Use PyTorch MPS for acceleration
                    chunk_tensor = torch.tensor(chunk, device=DEVICE, dtype=torch.float32)

                    rms = float(torch.sqrt(torch.mean(chunk_tensor**2)).cpu())
                    peak_to_peak = float((torch.max(chunk_tensor) - torch.min(chunk_tensor)).cpu())
                    energy = float(torch.sum(chunk_tensor**2).cpu())
                    std_val = float(torch.std(chunk_tensor).cpu())
                    mean_val = float(torch.mean(chunk_tensor).cpu())

                elif MLX_AVAILABLE:
                    # Use MLX for acceleration
                    chunk_mx = mx.array(chunk.astype(np.float32))

                    rms = float(mx.sqrt(mx.mean(chunk_mx**2)).item())
                    peak_to_peak = float((mx.max(chunk_mx) - mx.min(chunk_mx)).item())
                    energy = float(mx.sum(chunk_mx**2).item())
                    std_val = float(mx.std(chunk_mx).item())
                    mean_val = float(mx.mean(chunk_mx).item())

                else:
                    # Fallback to NumPy
                    rms = np.sqrt(np.mean(chunk**2))
                    peak_to_peak = np.max(chunk) - np.min(chunk)
                    energy = np.sum(chunk**2)
                    std_val = np.std(chunk)
                    mean_val = np.mean(chunk)

                # Statistical features
                try:
                    kurt = float(kurtosis(chunk, nan_policy='omit'))
                    skw = float(skew(chunk, nan_policy='omit'))
                except:
                    kurt = 0.0
                    skw = 0.0

                # Frequency domain features
                fft_vals = fft(chunk)
                fft_magnitude = np.abs(fft_vals)[:window_size//2]
                freqs = np.fft.fftfreq(window_size, 1/sampling_freq)[:window_size//2]

                if len(fft_magnitude) > 0:
                    dominant_freq = float(freqs[np.argmax(fft_magnitude)])
                    spectral_power = float(np.sum(fft_magnitude**2) / window_size)
                    spectral_centroid = float(np.sum(freqs * fft_magnitude) / np.sum(fft_magnitude)) if np.sum(fft_magnitude) != 0 else 0.0

                    # Apple Silicon optimized spectral features
                    if MPS_AVAILABLE:
                        try:
                            fft_tensor = torch.tensor(fft_magnitude.astype(np.float32), device=DEVICE, dtype=torch.float32)
                            spectral_rolloff = float(torch.quantile(fft_tensor, 0.85).cpu())
                            spectral_flux = float(torch.mean(torch.diff(fft_tensor)).cpu())
                        except:
                            spectral_rolloff = float(np.percentile(fft_magnitude, 85))
                            spectral_flux = float(np.mean(np.diff(fft_magnitude)))
                    else:
                        spectral_rolloff = float(np.percentile(fft_magnitude, 85))
                        spectral_flux = float(np.mean(np.diff(fft_magnitude)))
                else:
                    dominant_freq = spectral_power = spectral_centroid = 0.0
                    spectral_rolloff = spectral_flux = 0.0

                # Wavelet features (Apple Silicon optimized)
                try:
                    coeffs = pywt.wavedec(chunk, 'db4', level=3)
                    wavelet_energy = float(sum(np.sum(c**2) for c in coeffs))
                    wavelet_entropy = float(-sum(np.sum(c**2) * np.log(np.sum(c**2) + 1e-10) for c in coeffs))
                except:
                    wavelet_energy = 0.0
                    wavelet_entropy = 0.0

                # Crest factor and shape factor
                crest_factor = float(np.max(np.abs(chunk)) / rms) if rms != 0 else 0.0
                shape_factor = float(rms / mean_val) if mean_val != 0 else 0.0

                feat = {
                    f'sensor_{sensor}_rms': rms,
                    f'sensor_{sensor}_peak_to_peak': peak_to_peak,
                    f'sensor_{sensor}_energy': energy,
                    f'sensor_{sensor}_std': std_val,
                    f'sensor_{sensor}_mean': mean_val,
                    f'sensor_{sensor}_kurtosis': kurt,
                    f'sensor_{sensor}_skewness': skw,
                    f'sensor_{sensor}_dominant_freq': dominant_freq,
                    f'sensor_{sensor}_spectral_power': spectral_power,
                    f'sensor_{sensor}_spectral_centroid': spectral_centroid,
                    f'sensor_{sensor}_spectral_rolloff': spectral_rolloff,
                    f'sensor_{sensor}_spectral_flux': spectral_flux,
                    f'sensor_{sensor}_wavelet_energy': wavelet_energy,
                    f'sensor_{sensor}_wavelet_entropy': wavelet_entropy,
                    f'sensor_{sensor}_crest_factor': crest_factor,
                    f'sensor_{sensor}_shape_factor': shape_factor
                }

                batch_features.append(feat)

            sensor_features.extend(batch_features)

        features_list.append(pd.DataFrame(sensor_features))

    return pd.concat(features_list, axis=1) if features_list else pd.DataFrame()

def create_apple_models():
    """Create models optimized for Apple Silicon"""
    models = {}

    # XGBoost (CPU optimized for Apple Silicon)
    models['XGBoost'] = xgb.XGBClassifier(
        n_estimators=200,
        max_depth=6,
        learning_rate=0.1,
        random_state=42,
        n_jobs=-1,  # Use all CPU cores
        tree_method='hist'  # Optimized for Apple Silicon
    )

    # Random Forest (optimized for Apple Silicon)
    models['RandomForest'] = RandomForestClassifier(
        n_estimators=100,
        max_depth=10,
        random_state=42,
        n_jobs=-1  # Use all CPU cores
    )

    # SVM with optimized parameters
    models['SVM'] = SVC(
        kernel='rbf',
        probability=True,
        random_state=42
    )

    # Neural Network (scikit-learn)
    models['MLP'] = MLPClassifier(
        hidden_layer_sizes=(128, 64),
        max_iter=500,
        random_state=42
    )

    # Logistic Regression
    models['LogisticRegression'] = LogisticRegression(
        random_state=42,
        max_iter=1000,
        n_jobs=-1
    )

    return models

def main_apple_optimized():
    """Apple Silicon optimized main pipeline"""
    print("🍎" + "="*79)
    print("APPLE SILICON GPU-ACCELERATED BRIDGE HEALTH MONITORING")
    print("Optimized for Mac M1/M2/M3 with MLX and Metal Performance Shaders")
    print("🍎" + "="*79)

    start_time = time.time()
    base_path = "/Users/<USER>/Documents/bridge_sensor/data/"
    os.makedirs("apple_results", exist_ok=True)

    # Load data optimized for Apple Silicon
    print(f"\n🚀 1. APPLE SILICON DATA LOADING (ALL FILES)")
    print("-" * 50)
    healthy_data = load_sensor_data_apple(base_path, "healthy", MAX_SAMPLES)
    unhealthy_data = load_sensor_data_apple(base_path, "unhealthy", MAX_SAMPLES)

    if not healthy_data and not unhealthy_data:
        print("❌ Error: No data loaded.")
        return

    print(f"✅ Loaded {len(healthy_data)} healthy and {len(unhealthy_data)} unhealthy samples")

    # Apple Silicon optimized feature extraction
    print(f"\n⚡ 2. APPLE SILICON FEATURE EXTRACTION")
    print("-" * 50)

    X_data = []
    y_data = []

    # Process healthy data
    for i, df in enumerate(healthy_data):
        print(f"🟢 Processing healthy sample {i+1}/{len(healthy_data)}")
        features = extract_features_apple_optimized(df)
        if not features.empty:
            X_data.append(features)
            y_data.extend([1] * len(features))

    # Process unhealthy data
    for i, df in enumerate(unhealthy_data):
        print(f"🔴 Processing unhealthy sample {i+1}/{len(unhealthy_data)}")
        features = extract_features_apple_optimized(df)
        if not features.empty:
            X_data.append(features)
            y_data.extend([0] * len(features))

    if not X_data:
        print("❌ Error: No features extracted.")
        return

    X = pd.concat(X_data, axis=0)
    y = np.array(y_data)

    print(f"\n✅ Feature extraction completed:")
    print(f"📊 Feature matrix shape: {X.shape}")
    print(f"🎯 Class distribution: Healthy={sum(y==1)}, Unhealthy={sum(y==0)}")

    # Handle NaN values before feature selection
    print(f"\n🔧 3. DATA CLEANING AND PREPROCESSING")
    print("-" * 45)

    # Check for NaN values
    nan_count = X.isna().sum().sum()
    inf_count = np.isinf(X).sum().sum()
    print(f"🔍 Found {nan_count} NaN values and {inf_count} infinite values")

    # Replace NaN and infinite values
    X_clean = X.fillna(X.median())
    X_clean = X_clean.replace([np.inf, -np.inf], [X_clean.max().max(), X_clean.min().min()])

    print(f"✅ Data cleaned: {X_clean.shape}")

    # Feature selection
    print(f"\n🔍 4. INTELLIGENT FEATURE SELECTION")
    print("-" * 40)
    n_features = min(30, X_clean.shape[1])
    selector = SelectKBest(score_func=mutual_info_classif, k=n_features)
    X_selected = selector.fit_transform(X_clean, y)

    print(f"✅ Selected {X_selected.shape[1]} most informative features")

    # Train-test split
    X_train, X_test, y_train, y_test = train_test_split(
        X_selected, y, test_size=0.2, random_state=42, stratify=y
    )

    # Scaling
    scaler = RobustScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)

    # Apple Silicon optimized models
    print(f"\n🧠 5. APPLE SILICON MACHINE LEARNING")
    print("-" * 45)
    models = create_apple_models()

    # Add MLX model if available
    if MLX_AVAILABLE:
        try:
            models['MLX_Neural_Net'] = AppleMLXClassifier(X_train_scaled.shape[1])
        except Exception as e:
            print(f"⚠️ MLX model creation failed: {e}")

    results = {}
    for name, model in models.items():
        print(f"🔥 Training {name}...")
        model_start = time.time()

        try:
            model.fit(X_train_scaled, y_train)
            y_pred = model.predict(X_test_scaled)

            # Calculate metrics
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred)
            recall = recall_score(y_test, y_pred)
            f1 = f1_score(y_test, y_pred)

            training_time = time.time() - model_start

            results[name] = {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'training_time': training_time
            }

            print(f"✅ {name}: Acc={accuracy:.3f}, F1={f1:.3f}, Time={training_time:.2f}s")

        except Exception as e:
            print(f"❌ {name} failed: {e}")

    # Save results
    total_time = time.time() - start_time

    summary = {
        'total_runtime': f"{total_time:.2f} seconds",
        'apple_silicon_optimized': APPLE_OPTIMIZED,
        'mlx_available': MLX_AVAILABLE,
        'mps_available': MPS_AVAILABLE,
        'samples_processed': len(X),
        'features_extracted': X.shape[1],
        'selected_features': X_selected.shape[1],
        'results': results
    }

    with open('apple_results/apple_gpu_results.json', 'w') as f:
        json.dump(summary, f, indent=2, default=str)

    # Results table
    if results:
        results_df = pd.DataFrame(results).T
        results_df.to_csv('apple_results/apple_performance.csv')

        # Find best model
        best_model = max(results.keys(), key=lambda k: results[k]['f1_score'])
        best_f1 = results[best_model]['f1_score']
    else:
        best_model = "None"
        best_f1 = 0

    print(f"\n🍎" + "="*79)
    print("APPLE SILICON PROCESSING COMPLETED! 🎉")
    print(f"⏱️  Total runtime: {total_time:.2f} seconds")
    print(f"🏆 Best model: {best_model}")
    print(f"📈 Best F1 Score: {best_f1:.3f}")
    print(f"💾 Results saved to apple_results/ directory")
    print("🍎" + "="*79)

if __name__ == "__main__":
    main_apple_optimized()
