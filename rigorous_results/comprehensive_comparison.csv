model_name,accuracy,precision,recall,f1_score,specificity,sensitivity,roc_auc,matthews_cc,cohen_kappa,cv_accuracy_mean,cv_accuracy_std,cv_f1_mean,cv_f1_std,true_positives,true_negatives,false_positives,false_negatives,training_time
XGBoost_Optimized,0.8361244019138756,0.743801652892562,0.8598726114649682,0.7976366322008862,0.8218390804597702,0.8598726114649682,0.9276667398784684,0.6660654938821913,0.661155948971622,0.8377514979501735,0.01604366404959921,0.8464390255599088,0.020090377929711706,270,429,93,44,4.10969614982605
<PERSON><PERSON><PERSON>_<PERSON>ost<PERSON>,0.819377990430622,0.7220708446866485,0.8439490445859873,0.7782672540381792,0.8045977011494253,0.8439490445859873,0.913488054274349,0.63288077967802,0.6274465824577972,0.8210774729317777,0.016787346698173104,0.8320208862351162,0.02107865350217332,265,420,102,49,45.148406982421875
SVM_RBF,0.8086124401913876,0.6964285714285714,0.8694267515923567,0.773371104815864,0.7720306513409961,0.8694267515923567,0.8955511628474511,0.6224914325976342,0.6112079990698756,0.7702466799817793,0.016353781609821393,0.7867289581921902,0.017157993749308556,273,403,119,41,9.708938121795654
MLP_Neural_Network,0.8074162679425837,0.7459807073954984,0.7388535031847133,0.7424,0.8486590038314177,0.7388535031847133,0.8790968104058374,0.5886502674329522,0.5886329739113425,0.8208209818143593,0.025522082803984096,0.8248123678024948,0.035812422214019826,232,443,79,82,36.59807109832764
RandomForest_Optimized,0.7870813397129187,0.6725888324873096,0.8439490445859873,0.748587570621469,0.7528735632183908,0.8439490445859873,0.8950508822022111,0.5790100419831007,0.5679912673897392,0.8012246399663617,0.015466747399825412,0.8149884743881257,0.020121964504985036,265,393,129,49,3.755847215652466
AdaBoost,0.7595693779904307,0.6395061728395062,0.8248407643312102,0.7204450625869263,0.7203065134099617,0.8248407643312102,0.8422346682285183,0.5282600602674054,0.51538887478947,0.7313399208101196,0.012619637228811677,0.7404102117770075,0.02188659137544953,259,376,146,55,9.743889093399048
KNN,0.7368421052631579,0.6087962962962963,0.8375796178343949,0.7050938337801609,0.6762452107279694,0.8375796178343949,0.8193803841179199,0.4979464099539463,0.47803382903848335,0.744848102596447,0.019187825370378633,0.7714189573997867,0.020612171231169672,263,353,169,51,0.2214491367340088
Decision_Tree,0.7284688995215312,0.6074074074074074,0.7834394904458599,0.6842837273991655,0.6954022988505747,0.7834394904458599,0.7847115455011349,0.4640085400782011,0.4527028585930831,0.7271120221451347,0.012342863304840404,0.7407030657528765,0.017466445047767794,246,363,159,68,0.7032508850097656
Logistic_Regression,0.7260765550239234,0.6029055690072639,0.7929936305732485,0.6850068775790922,0.685823754789272,0.7929936305732485,0.8115528223149571,0.46379362714233924,0.45051778374778995,0.7448263779389607,0.021446270690980505,0.7579543606496966,0.011577222499154981,249,358,164,65,0.20582222938537598
Naive_Bayes,0.4904306220095694,0.4090909090909091,0.802547770700637,0.5419354838709678,0.30268199233716475,0.802547770700637,0.6668313932205872,0.11572760661174954,0.08830817751745901,0.5722583131854655,0.021036855562909622,0.6635726329192385,0.016402891360258375,252,158,364,62,0.01985788345336914
