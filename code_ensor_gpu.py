#!/usr/bin/env python3
"""
GPU-Accelerated Bridge Structural Health Monitoring System
Optimized for RunPod A40 GPU
"""

import os
import pandas as pd
import numpy as np
import time
from datetime import datetime
import json
import warnings

# GPU acceleration imports
try:
    import cupy as cp
    import cudf
    GPU_AVAILABLE = True
    print("GPU acceleration available!")
except ImportError:
    import numpy as cp
    GPU_AVAILABLE = False
    print("GPU acceleration not available, using CPU")

# Core libraries
from scipy.fft import fft
from scipy.stats import kurtosis, skew, entropy, ttest_ind
from scipy.signal import welch, spectrogram, butter, filtfilt, find_peaks
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.preprocessing import RobustScaler
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import confusion_matrix, roc_curve, auc, accuracy_score, precision_score, recall_score, f1_score
from sklearn.feature_selection import SelectKBest, mutual_info_classif

# GPU-accelerated XGBoost
try:
    import xgboost as xgb
    XGBClassifier = xgb.XGBClassifier
    GPU_XGB = True
except ImportError:
    from sklearn.ensemble import GradientBoostingClassifier as XGBClassifier
    GPU_XGB = False

import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import seaborn as sns
import pywt

warnings.filterwarnings("ignore")

# GPU-optimized parameters
BATCH_SIZE = 1000 if GPU_AVAILABLE else 100
MAX_SAMPLES = 10  # Limit samples for faster processing
REDUCED_FEATURES = True  # Use subset of features for speed

print(f"GPU Available: {GPU_AVAILABLE}")
print(f"GPU XGBoost: {GPU_XGB}")
print(f"Batch Size: {BATCH_SIZE}")

def load_sensor_data_fast(base_path, condition, max_files=MAX_SAMPLES):
    """Fast data loading with limits"""
    data_list = []
    folder_path = os.path.join(base_path, condition)
    print(f"\nLoading {condition} data (max {max_files} files)...")
    
    if not os.path.exists(folder_path):
        print(f"Error: Directory {folder_path} does not exist.")
        return data_list
    
    file_count = 0
    for root, _, files in os.walk(folder_path):
        for file in files:
            if file.endswith('.xlsx') and file_count < max_files:
                file_path = os.path.join(root, file)
                try:
                    df = pd.read_excel(file_path, usecols=range(6))
                    print(f"Loaded {file}: Shape {df.shape}")
                    data_list.append(df)
                    file_count += 1
                except Exception as e:
                    print(f"Error loading {file}: {str(e)}")
    
    return data_list

def extract_features_gpu_optimized(df, sampling_freq=1651, start_sec=2, duration_sec=10, window_sec=1.0):
    """GPU-optimized feature extraction with reduced complexity"""
    start_idx = start_sec * sampling_freq
    end_idx = start_idx + (duration_sec * sampling_freq)
    
    if start_idx >= len(df):
        return pd.DataFrame()
    if end_idx > len(df):
        end_idx = len(df)
    
    df_chunk = df.iloc[start_idx:end_idx]
    window_size = int(window_sec * sampling_freq)
    n_windows = len(df_chunk) // window_size
    
    if n_windows == 0:
        return pd.DataFrame()
    
    features_list = []
    
    for sensor in range(6):
        sensor_data = df_chunk.iloc[:, sensor].values
        sensor_features = []
        
        # Process in batches for GPU
        for i in range(0, n_windows, BATCH_SIZE):
            batch_end = min(i + BATCH_SIZE, n_windows)
            batch_features = []
            
            for j in range(i, batch_end):
                chunk = sensor_data[j*window_size:(j+1)*window_size]
                
                if GPU_AVAILABLE:
                    # Move to GPU
                    chunk_gpu = cp.array(chunk)
                    
                    # GPU-accelerated computations
                    rms = float(cp.sqrt(cp.mean(chunk_gpu**2)))
                    peak_to_peak = float(cp.max(chunk_gpu) - cp.min(chunk_gpu))
                    energy = float(cp.sum(chunk_gpu**2))
                    std_val = float(cp.std(chunk_gpu))
                    mean_val = float(cp.mean(chunk_gpu))
                    
                    # Move back to CPU for scipy functions
                    chunk = cp.asnumpy(chunk_gpu)
                else:
                    # CPU computations
                    rms = np.sqrt(np.mean(chunk**2))
                    peak_to_peak = np.max(chunk) - np.min(chunk)
                    energy = np.sum(chunk**2)
                    std_val = np.std(chunk)
                    mean_val = np.mean(chunk)
                
                # Essential features only for speed
                kurt = kurtosis(chunk, nan_policy='omit')
                skw = skew(chunk, nan_policy='omit')
                
                # Frequency domain (essential features only)
                fft_vals = fft(chunk)
                fft_magnitude = np.abs(fft_vals)[:window_size//2]
                freqs = np.fft.fftfreq(window_size, 1/sampling_freq)[:window_size//2]
                
                if len(fft_magnitude) > 0:
                    dominant_freq = freqs[np.argmax(fft_magnitude)]
                    spectral_power = np.sum(fft_magnitude**2) / window_size
                    spectral_centroid = np.sum(freqs * fft_magnitude) / np.sum(fft_magnitude) if np.sum(fft_magnitude) != 0 else 0
                else:
                    dominant_freq = spectral_power = spectral_centroid = 0
                
                # Wavelet features (simplified)
                if not REDUCED_FEATURES:
                    try:
                        coeffs = pywt.wavedec(chunk, 'db4', level=3)
                        wavelet_energy = sum(np.sum(c**2) for c in coeffs)
                    except:
                        wavelet_energy = 0
                else:
                    wavelet_energy = 0
                
                feat = {
                    f'sensor_{sensor}_rms': rms,
                    f'sensor_{sensor}_peak_to_peak': peak_to_peak,
                    f'sensor_{sensor}_energy': energy,
                    f'sensor_{sensor}_std': std_val,
                    f'sensor_{sensor}_mean': mean_val,
                    f'sensor_{sensor}_kurtosis': kurt,
                    f'sensor_{sensor}_skewness': skw,
                    f'sensor_{sensor}_dominant_freq': dominant_freq,
                    f'sensor_{sensor}_spectral_power': spectral_power,
                    f'sensor_{sensor}_spectral_centroid': spectral_centroid,
                    f'sensor_{sensor}_wavelet_energy': wavelet_energy
                }
                
                batch_features.append(feat)
            
            sensor_features.extend(batch_features)
        
        features_list.append(pd.DataFrame(sensor_features))
    
    return pd.concat(features_list, axis=1) if features_list else pd.DataFrame()

def create_gpu_models():
    """Create models optimized for GPU"""
    models = {}
    
    # GPU-accelerated XGBoost
    if GPU_XGB:
        models['XGBoost_GPU'] = XGBClassifier(
            tree_method='gpu_hist',
            gpu_id=0,
            random_state=42,
            n_estimators=100,  # Reduced for speed
            max_depth=6,
            learning_rate=0.1
        )
    
    # CPU models (optimized parameters)
    models['RandomForest'] = RandomForestClassifier(
        n_estimators=50,  # Reduced for speed
        max_depth=8,
        random_state=42,
        n_jobs=-1
    )
    
    models['SVM'] = SVC(
        kernel='rbf',
        probability=True,
        random_state=42
    )
    
    models['LogisticRegression'] = LogisticRegression(
        random_state=42,
        max_iter=500
    )
    
    return models

def main_gpu_optimized():
    """GPU-optimized main pipeline"""
    print("="*80)
    print("GPU-ACCELERATED BRIDGE HEALTH MONITORING SYSTEM")
    print("Optimized for RunPod A40 GPU")
    print("="*80)
    
    start_time = time.time()
    base_path = "/content/bridge_sensor/data/"  # RunPod path
    os.makedirs("results", exist_ok=True)
    
    # Load limited data for speed
    print(f"\n1. FAST DATA LOADING (max {MAX_SAMPLES} files each)")
    print("-" * 50)
    healthy_data = load_sensor_data_fast(base_path, "healthy", MAX_SAMPLES)
    unhealthy_data = load_sensor_data_fast(base_path, "unhealthy", MAX_SAMPLES)
    
    if not healthy_data and not unhealthy_data:
        print("Error: No data loaded.")
        return
    
    print(f"Loaded {len(healthy_data)} healthy and {len(unhealthy_data)} unhealthy samples")
    
    # Fast feature extraction
    print(f"\n2. GPU-OPTIMIZED FEATURE EXTRACTION")
    print("-" * 45)
    
    X_data = []
    y_data = []
    
    # Process healthy data
    for i, df in enumerate(healthy_data):
        print(f"Processing healthy sample {i+1}/{len(healthy_data)}")
        features = extract_features_gpu_optimized(df)
        if not features.empty:
            X_data.append(features)
            y_data.extend([1] * len(features))
    
    # Process unhealthy data
    for i, df in enumerate(unhealthy_data):
        print(f"Processing unhealthy sample {i+1}/{len(unhealthy_data)}")
        features = extract_features_gpu_optimized(df)
        if not features.empty:
            X_data.append(features)
            y_data.extend([0] * len(features))
    
    if not X_data:
        print("Error: No features extracted.")
        return
    
    X = pd.concat(X_data, axis=0)
    y = np.array(y_data)
    
    print(f"\nFeature extraction completed:")
    print(f"- Feature matrix shape: {X.shape}")
    print(f"- Class distribution: Healthy={sum(y==1)}, Unhealthy={sum(y==0)}")
    
    # Fast feature selection
    print(f"\n3. FEATURE SELECTION")
    print("-" * 25)
    n_features = min(20, X.shape[1])  # Limit features for speed
    selector = SelectKBest(score_func=mutual_info_classif, k=n_features)
    X_selected = selector.fit_transform(X, y)
    
    print(f"Selected {X_selected.shape[1]} features")
    
    # Train-test split
    X_train, X_test, y_train, y_test = train_test_split(
        X_selected, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # Scaling
    scaler = RobustScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # GPU-optimized models
    print(f"\n4. GPU-ACCELERATED MACHINE LEARNING")
    print("-" * 40)
    models = create_gpu_models()
    
    results = {}
    for name, model in models.items():
        print(f"Training {name}...")
        model_start = time.time()
        
        model.fit(X_train_scaled, y_train)
        y_pred = model.predict(X_test_scaled)
        
        # Calculate metrics
        accuracy = accuracy_score(y_test, y_pred)
        precision = precision_score(y_test, y_pred)
        recall = recall_score(y_test, y_pred)
        f1 = f1_score(y_test, y_pred)
        
        training_time = time.time() - model_start
        
        results[name] = {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'training_time': training_time
        }
        
        print(f"{name}: Acc={accuracy:.3f}, F1={f1:.3f}, Time={training_time:.2f}s")
    
    # Save results
    total_time = time.time() - start_time
    
    summary = {
        'total_runtime': f"{total_time:.2f} seconds",
        'gpu_available': GPU_AVAILABLE,
        'samples_processed': len(X),
        'features_extracted': X.shape[1],
        'selected_features': X_selected.shape[1],
        'results': results
    }
    
    with open('results/gpu_results.json', 'w') as f:
        json.dump(summary, f, indent=2, default=str)
    
    # Results table
    results_df = pd.DataFrame(results).T
    results_df.to_csv('results/gpu_performance.csv')
    
    print(f"\n" + "="*80)
    print("GPU-ACCELERATED PROCESSING COMPLETED!")
    print(f"Total runtime: {total_time:.2f} seconds")
    print(f"Best F1 Score: {max(r['f1_score'] for r in results.values()):.3f}")
    print("Results saved to results/ directory")
    print("="*80)

if __name__ == "__main__":
    main_gpu_optimized()
