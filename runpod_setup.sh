#!/bin/bash
# RunPod A40 GPU Setup Script for Bridge Sensor Analysis

echo "🚀 Setting up GPU-Accelerated Bridge Sensor Analysis on RunPod A40"
echo "=================================================================="

# Update system
apt-get update -y

# Install system dependencies
apt-get install -y wget curl git

# Install Python dependencies
echo "📦 Installing Python packages..."
pip install --upgrade pip

# Core scientific computing
pip install numpy pandas scipy scikit-learn matplotlib seaborn openpyxl

# GPU acceleration
echo "🔥 Installing GPU-accelerated libraries..."
pip install cupy-cuda11x
pip install xgboost[gpu]

# Signal processing
pip install PyWavelets

# Additional ML libraries
pip install tqdm

# Verify GPU setup
echo "🔍 Verifying GPU setup..."
python -c "
import cupy as cp
import xgboost as xgb
print(f'CuPy version: {cp.__version__}')
print(f'XGBoost version: {xgb.__version__}')
print(f'GPU devices: {cp.cuda.runtime.getDeviceCount()}')
print('GPU setup successful!')
"

# Create directory structure
echo "📁 Creating directory structure..."
mkdir -p /content/bridge_sensor/data
mkdir -p /content/bridge_sensor/results
mkdir -p /content/bridge_sensor/img

echo "✅ Setup complete! Ready for GPU-accelerated processing."
echo ""
echo "📋 Next steps:"
echo "1. Upload your data to /content/bridge_sensor/data/"
echo "2. Run: python code_ensor_gpu.py"
echo "3. Results will be saved to /content/bridge_sensor/results/"
echo ""
echo "⚡ Expected speedup with A40 GPU: 10-100x faster!"
