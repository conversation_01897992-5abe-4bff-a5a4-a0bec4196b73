{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading healthy file: healthy/traindata_201911/traindata_20191107_080636.xlsx\n", "Loading healthy file: healthy/traindata_201911/traindata_20191102_171055.xlsx\n", "Loading healthy file: healthy/traindata_201911/traindata_20191115_080506.xlsx\n", "Loading unhealthy file: unhealthy/traindata_201903/traindata_20190313_080000.xlsx\n", "Loading unhealthy file: unhealthy/traindata_201903/traindata_20190313_170024.xlsx\n", "Loading unhealthy file: unhealthy/traindata_201903/traindata_20190329_081100.xlsx\n", "Healthy features shape: (18, 6)\n", "Unhealthy features shape: (18, 6)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/dl-env/lib/python3.10/site-packages/sklearn/impute/_base.py:598: UserWarning: Skipping features without any observed values: ['skewness' 'kurtosis' 'autocorrelation' 'fft_magnitude']. At least one non-missing value is needed for imputation with strategy='mean'.\n", "  warnings.warn(\n", "/Users/<USER>/dl-env/lib/python3.10/site-packages/sklearn/impute/_base.py:598: UserWarning: Skipping features without any observed values: ['skewness' 'kurtosis' 'autocorrelation' 'fft_magnitude']. At least one non-missing value is needed for imputation with strategy='mean'.\n", "  warnings.warn(\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "# Example usage of the code\n", "\n", "\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.impute import SimpleImputer\n", "from scipy.stats import skew, kurtosis\n", "from scipy.fft import fft\n", "import matplotlib.pyplot as plt\n", "\n", "# Step 1: Load and preprocess data\n", "def load_and_preprocess_data(healthy_folder_path, unhealthy_folder_path):\n", "    healthy_data = []\n", "    unhealthy_data = []\n", "\n", "    # Select the first 3 files from each folder for testing\n", "    healthy_files = os.listdir(healthy_folder_path)[:3]  # Only the first 3 files from the healthy class\n", "    unhealthy_files = os.listdir(unhealthy_folder_path)[:3]  # Only the first 3 files from the unhealthy class\n", "\n", "    # Process the healthy data\n", "    for file_name in healthy_files:\n", "        if file_name.endswith('.xlsx'):\n", "            file_path = os.path.join(healthy_folder_path, file_name)\n", "            print(f\"Loading healthy file: {file_path}\")\n", "            df = pd.read_excel(file_path)\n", "            df = df.iloc[:, :6]  # Use only first 6 columns\n", "            df.fillna(df.median(), inplace=True)  # Handle NaN values by filling with median\n", "            healthy_data.append(df)\n", "\n", "    # Process the unhealthy data\n", "    for file_name in unhealthy_files:\n", "        if file_name.endswith('.xlsx'):\n", "            file_path = os.path.join(unhealthy_folder_path, file_name)\n", "            print(f\"Loading unhealthy file: {file_path}\")\n", "            df = pd.read_excel(file_path)\n", "            df = df.iloc[:, :6]  # Use only first 6 columns\n", "            df.fillna(df.median(), inplace=True)  # Handle NaN values by filling with median\n", "            unhealthy_data.append(df)\n", "\n", "    # Combine both healthy and unhealthy data into single DataFrames\n", "    healthy_data_df = pd.concat(healthy_data, ignore_index=True) if healthy_data else pd.DataFrame()\n", "    unhealthy_data_df = pd.concat(unhealthy_data, ignore_index=True) if unhealthy_data else pd.DataFrame()\n", "\n", "    return healthy_data_df, unhealthy_data_df\n", "\n", "# Step 2: Feature Extraction (Statistical and Fourier Features)\n", "def extract_features(df):\n", "    features = []\n", "    for sensor in df.columns:\n", "        # Get the time-series data for the sensor\n", "        time_series = df[sensor]\n", "        \n", "        # Statistical features\n", "        mean = np.mean(time_series)\n", "        var = np.var(time_series)\n", "        skewness = skew(time_series)\n", "        kurt = kurtosis(time_series)\n", "        \n", "        # Autocorrelation (lag 1)\n", "        autocorr = np.corrcoef(time_series[:-1], time_series[1:])[0, 1]\n", "        \n", "        # Fourier Transform (Frequency domain features)\n", "        fft_vals = fft(time_series)\n", "        fft_real = np.real(fft_vals)\n", "        fft_imag = np.imag(fft_vals)\n", "        fft_magnitude = np.abs(fft_vals).mean()  # Mean magnitude of the FFT\n", "        \n", "        # Append the features for this sensor\n", "        features.append([mean, var, skewness, kurt, autocorr, fft_magnitude])\n", "\n", "    # Convert the list of features into a DataFrame\n", "    feature_df = pd.DataFrame(features, columns=[\n", "        'mean', 'variance', 'skewness', 'kurtosis', 'autocorrelation', 'fft_magnitude'\n", "    ])\n", "    return feature_df\n", "\n", "# Step 3: Standardize the features\n", "def standardize_features(healthy_features, unhealthy_features):\n", "    # Impute missing values if any NaN values exist\n", "    imputer = SimpleImputer(strategy='mean')\n", "    healthy_features_imputed = imputer.fit_transform(healthy_features)\n", "    unhealthy_features_imputed = imputer.transform(unhealthy_features)\n", "\n", "    # Scale the features\n", "    scaler = StandardScaler()\n", "    healthy_features_scaled = scaler.fit_transform(healthy_features_imputed)\n", "    unhealthy_features_scaled = scaler.transform(unhealthy_features_imputed)\n", "\n", "    return healthy_features_scaled, unhealthy_features_scaled\n", "\n", "# Step 4: Visualize the features\n", "def visualize_data(features_scaled, label):\n", "    plt.figure(figsize=(10, 6))\n", "    plt.plot(features_scaled, label=f'{label} Features')\n", "    plt.title(f'{label} Features Visualization')\n", "    plt.xlabel('Time')\n", "    plt.ylabel('Feature Values')\n", "    plt.legend()\n", "    plt.show()\n", "healthy_folder_path = 'healthy/traindata_201911'  # Replace with your healthy data path\n", "unhealthy_folder_path = 'unhealthy/traindata_201903'  # Replace with your unhealthy data path\n", "\n", "# Step 1: Load and preprocess data from both healthy and unhealthy folders\n", "healthy_data, unhealthy_data = load_and_preprocess_data(healthy_folder_path, unhealthy_folder_path)\n", "\n", "# Step 2: Extract features from both datasets\n", "healthy_features = extract_features(healthy_data)\n", "unhealthy_features = extract_features(unhealthy_data)\n", "\n", "# Check if features are successfully extracted\n", "if healthy_features.empty:\n", "    print(\"Healthy feature extraction failed.\")\n", "else:\n", "    print(f\"Healthy features shape: {healthy_features.shape}\")\n", "\n", "if unhealthy_features.empty:\n", "    print(\"Unhealthy feature extraction failed.\")\n", "else:\n", "    print(f\"Unhealthy features shape: {unhealthy_features.shape}\")\n", "\n", "# Step 3: Standardize features\n", "healthy_features_scaled, unhealthy_features_scaled = standardize_features(healthy_features, unhealthy_features)\n", "\n", "# Step 4: Visualize the features\n", "visualize_data(healthy_features_scaled, 'Healthy')\n", "visualize_data(unhealthy_features_scaled, 'Unhealthy')"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading file: heathy_small/traindata_201911/traindata_20191027_171117.xlsx\n", "Loading file: heathy_small/traindata_201911/traindata_20191028_081019.xlsx\n", "Loading file: heathy_small/traindata_201911/traindata_20191025_160516.xlsx\n", "Loading file: unhealthy_small/traindata_201903/traindata_20190301_080625.xlsx\n", "Loading file: unhealthy_small/traindata_201903/traindata_20190321_170152.xlsx\n", "Loading file: unhealthy_small/traindata_201903/traindata_20190306_170441.xlsx\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/dl-env/lib/python3.10/site-packages/sklearn/utils/extmath.py:1137: RuntimeWarning: invalid value encountered in divide\n", "  updated_mean = (last_sum + new_sum) / updated_sample_count\n", "/Users/<USER>/dl-env/lib/python3.10/site-packages/sklearn/utils/extmath.py:1142: RuntimeWarning: invalid value encountered in divide\n", "  T = new_sum / new_sample_count\n", "/Users/<USER>/dl-env/lib/python3.10/site-packages/sklearn/utils/extmath.py:1162: RuntimeWarning: invalid value encountered in divide\n", "  new_unnormalized_variance -= correction**2 / new_sample_count\n"]}, {"ename": "ValueError", "evalue": "Input X contains NaN.\nIsolationForest does not accept missing values encoded as NaN natively. For supervised learning, you might want to consider sklearn.ensemble.HistGradientBoostingClassifier and Regressor which accept missing values encoded as NaNs natively. Alternatively, it is possible to preprocess the data, for instance by using an imputer transformer in a pipeline or drop samples with missing values. See https://scikit-learn.org/stable/modules/impute.html You can find a list of all estimators that handle NaN values at the following page: https://scikit-learn.org/stable/modules/impute.html#estimators-that-handle-nan-values", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[2], line 114\u001b[0m\n\u001b[1;32m    111\u001b[0m healthy_features_scaled, unhealthy_features_scaled \u001b[38;5;241m=\u001b[39m standardize_features(healthy_features, unhealthy_features)\n\u001b[1;32m    113\u001b[0m \u001b[38;5;66;03m# Step 4: Train the anomaly detection model\u001b[39;00m\n\u001b[0;32m--> 114\u001b[0m model \u001b[38;5;241m=\u001b[39m \u001b[43mbuild_anomaly_detection_model\u001b[49m\u001b[43m(\u001b[49m\u001b[43mhealthy_features_scaled\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43munhealthy_features_scaled\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    116\u001b[0m \u001b[38;5;66;03m# Step 5: Predict anomalies\u001b[39;00m\n\u001b[1;32m    117\u001b[0m healthy_predictions \u001b[38;5;241m=\u001b[39m model\u001b[38;5;241m.\u001b[39mpredict(healthy_features_scaled)\n", "Cell \u001b[0;32mIn[2], line 77\u001b[0m, in \u001b[0;36mbuild_anomaly_detection_model\u001b[0;34m(healthy_features_scaled, unhealthy_features_scaled)\u001b[0m\n\u001b[1;32m     75\u001b[0m \u001b[38;5;66;03m# Fit the model using both healthy and unhealthy data\u001b[39;00m\n\u001b[1;32m     76\u001b[0m combined_features \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39mconcatenate([healthy_features_scaled, unhealthy_features_scaled], axis\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0\u001b[39m)\n\u001b[0;32m---> 77\u001b[0m \u001b[43mmodel\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfit\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcombined_features\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     78\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m model\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/sklearn/base.py:1473\u001b[0m, in \u001b[0;36m_fit_context.<locals>.decorator.<locals>.wrapper\u001b[0;34m(estimator, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1466\u001b[0m     estimator\u001b[38;5;241m.\u001b[39m_validate_params()\n\u001b[1;32m   1468\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m config_context(\n\u001b[1;32m   1469\u001b[0m     skip_parameter_validation\u001b[38;5;241m=\u001b[39m(\n\u001b[1;32m   1470\u001b[0m         prefer_skip_nested_validation \u001b[38;5;129;01mor\u001b[39;00m global_skip_validation\n\u001b[1;32m   1471\u001b[0m     )\n\u001b[1;32m   1472\u001b[0m ):\n\u001b[0;32m-> 1473\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfit_method\u001b[49m\u001b[43m(\u001b[49m\u001b[43mestimator\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/sklearn/ensemble/_iforest.py:291\u001b[0m, in \u001b[0;36mIsolationForest.fit\u001b[0;34m(self, X, y, sample_weight)\u001b[0m\n\u001b[1;32m    268\u001b[0m \u001b[38;5;129m@_fit_context\u001b[39m(prefer_skip_nested_validation\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[1;32m    269\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mfit\u001b[39m(\u001b[38;5;28mself\u001b[39m, X, y\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, sample_weight\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[1;32m    270\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    271\u001b[0m \u001b[38;5;124;03m    Fit estimator.\u001b[39;00m\n\u001b[1;32m    272\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    289\u001b[0m \u001b[38;5;124;03m        Fitted estimator.\u001b[39;00m\n\u001b[1;32m    290\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 291\u001b[0m     X \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_validate_data\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maccept_sparse\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcsc\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdtype\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtree_dtype\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    292\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m issparse(X):\n\u001b[1;32m    293\u001b[0m         \u001b[38;5;66;03m# Pre-sort indices to avoid that each individual tree of the\u001b[39;00m\n\u001b[1;32m    294\u001b[0m         \u001b[38;5;66;03m# ensemble sorts the indices.\u001b[39;00m\n\u001b[1;32m    295\u001b[0m         X\u001b[38;5;241m.\u001b[39msort_indices()\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/sklearn/base.py:633\u001b[0m, in \u001b[0;36mBaseEstimator._validate_data\u001b[0;34m(self, X, y, reset, validate_separately, cast_to_ndarray, **check_params)\u001b[0m\n\u001b[1;32m    631\u001b[0m         out \u001b[38;5;241m=\u001b[39m X, y\n\u001b[1;32m    632\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m no_val_X \u001b[38;5;129;01mand\u001b[39;00m no_val_y:\n\u001b[0;32m--> 633\u001b[0m     out \u001b[38;5;241m=\u001b[39m \u001b[43mcheck_array\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minput_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mX\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mcheck_params\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    634\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m no_val_X \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m no_val_y:\n\u001b[1;32m    635\u001b[0m     out \u001b[38;5;241m=\u001b[39m _check_y(y, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mcheck_params)\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/sklearn/utils/validation.py:1064\u001b[0m, in \u001b[0;36mcheck_array\u001b[0;34m(array, accept_sparse, accept_large_sparse, dtype, order, copy, force_writeable, force_all_finite, ensure_2d, allow_nd, ensure_min_samples, ensure_min_features, estimator, input_name)\u001b[0m\n\u001b[1;32m   1058\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m   1059\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFound array with dim \u001b[39m\u001b[38;5;132;01m%d\u001b[39;00m\u001b[38;5;124m. \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m expected <= 2.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1060\u001b[0m         \u001b[38;5;241m%\u001b[39m (array\u001b[38;5;241m.\u001b[39mndim, estimator_name)\n\u001b[1;32m   1061\u001b[0m     )\n\u001b[1;32m   1063\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m force_all_finite:\n\u001b[0;32m-> 1064\u001b[0m     \u001b[43m_assert_all_finite\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1065\u001b[0m \u001b[43m        \u001b[49m\u001b[43marray\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1066\u001b[0m \u001b[43m        \u001b[49m\u001b[43minput_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43minput_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1067\u001b[0m \u001b[43m        \u001b[49m\u001b[43mestimator_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mestimator_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1068\u001b[0m \u001b[43m        \u001b[49m\u001b[43mallow_nan\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mforce_all_finite\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m==\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mallow-nan\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1069\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1071\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m copy:\n\u001b[1;32m   1072\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m _is_numpy_namespace(xp):\n\u001b[1;32m   1073\u001b[0m         \u001b[38;5;66;03m# only make a copy if `array` and `array_orig` may share memory`\u001b[39;00m\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/sklearn/utils/validation.py:123\u001b[0m, in \u001b[0;36m_assert_all_finite\u001b[0;34m(X, allow_nan, msg_dtype, estimator_name, input_name)\u001b[0m\n\u001b[1;32m    120\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m first_pass_isfinite:\n\u001b[1;32m    121\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m\n\u001b[0;32m--> 123\u001b[0m \u001b[43m_assert_all_finite_element_wise\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    124\u001b[0m \u001b[43m    \u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    125\u001b[0m \u001b[43m    \u001b[49m\u001b[43mxp\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mxp\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    126\u001b[0m \u001b[43m    \u001b[49m\u001b[43mallow_nan\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mallow_nan\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    127\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmsg_dtype\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmsg_dtype\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    128\u001b[0m \u001b[43m    \u001b[49m\u001b[43mestimator_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mestimator_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    129\u001b[0m \u001b[43m    \u001b[49m\u001b[43minput_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43minput_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    130\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/sklearn/utils/validation.py:172\u001b[0m, in \u001b[0;36m_assert_all_finite_element_wise\u001b[0;34m(X, xp, allow_nan, msg_dtype, estimator_name, input_name)\u001b[0m\n\u001b[1;32m    155\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m estimator_name \u001b[38;5;129;01mand\u001b[39;00m input_name \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mX\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mand\u001b[39;00m has_nan_error:\n\u001b[1;32m    156\u001b[0m     \u001b[38;5;66;03m# Improve the error message on how to handle missing values in\u001b[39;00m\n\u001b[1;32m    157\u001b[0m     \u001b[38;5;66;03m# scikit-learn.\u001b[39;00m\n\u001b[1;32m    158\u001b[0m     msg_err \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m    159\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00mestimator_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m does not accept missing values\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    160\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m encoded as NaN natively. For supervised learning, you might want\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    170\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m#estimators-that-handle-nan-values\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    171\u001b[0m     )\n\u001b[0;32m--> 172\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(msg_err)\n", "\u001b[0;31mValueError\u001b[0m: Input X contains NaN.\nIsolationForest does not accept missing values encoded as NaN natively. For supervised learning, you might want to consider sklearn.ensemble.HistGradientBoostingClassifier and Regressor which accept missing values encoded as NaNs natively. Alternatively, it is possible to preprocess the data, for instance by using an imputer transformer in a pipeline or drop samples with missing values. See https://scikit-learn.org/stable/modules/impute.html You can find a list of all estimators that handle NaN values at the following page: https://scikit-learn.org/stable/modules/impute.html#estimators-that-handle-nan-values"]}], "source": ["import os\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import pywt\n", "from scipy.stats import skew, kurtosis\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.decomposition import PCA\n", "from sklearn.ensemble import IsolationForest\n", "from sklearn.metrics import confusion_matrix, classification_report\n", "\n", "# Step 1: Load and preprocess the data from the folders\n", "def load_and_preprocess_data(healthy_folder_path, unhealthy_folder_path):\n", "    healthy_data, unhealthy_data = [], []\n", "    \n", "    # Helper function to load data from a folder\n", "    def load_data_from_folder(folder_path):\n", "        data = []\n", "        for subfolder in os.listdir(folder_path):\n", "            subfolder_path = os.path.join(folder_path, subfolder)\n", "            if os.path.isdir(subfolder_path):\n", "                for file_name in os.listdir(subfolder_path):\n", "                    if file_name.endswith('.xlsx'):\n", "                        file_path = os.path.join(subfolder_path, file_name)\n", "                        print(f\"Loading file: {file_path}\")\n", "                        df = pd.read_excel(file_path, usecols=range(6))  # Load only the first 6 columns\n", "                        df.fillna(df.median(), inplace=True)  # Handle missing values by filling with median\n", "                        data.append(df)\n", "        return pd.concat(data, ignore_index=True)\n", "    \n", "    # Load data from both healthy and unhealthy folders\n", "    healthy_data = load_data_from_folder(healthy_folder_path)\n", "    unhealthy_data = load_data_from_folder(unhealthy_folder_path)\n", "    \n", "    return healthy_data, unhealthy_data\n", "\n", "# Step 2: Feature extraction using various statistical methods and wavelet transform\n", "def extract_features(df):\n", "    features = []\n", "    \n", "    for sensor in df.columns:\n", "        # Wavelet decomposition (Using Daubechies 1 wavelet)\n", "        coeffs = pywt.wavedec(df[sensor], 'db1', level=3)\n", "        approx, detail1, detail2, detail3 = coeffs\n", "        \n", "        # Statistical features: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>\n", "        feature = [\n", "            np.mean(approx), np.var(approx), skew(approx), kurtosis(approx),  # Approximation coefficients\n", "            np.mean(detail1), np.var(detail1), skew(detail1), kurtosis(detail1),  # Detail coefficients at level 1\n", "            np.mean(detail2), np.var(detail2), skew(detail2), kurtosis(detail2),  # Detail coefficients at level 2\n", "            np.mean(detail3), np.var(detail3), skew(detail3), kurtosis(detail3)   # Detail coefficients at level 3\n", "        ]\n", "        features.append(feature)\n", "    \n", "    # Convert the list of features into a DataFrame\n", "    feature_df = pd.DataFrame(features, columns=[\n", "        'mean_approx', 'var_approx', 'skew_approx', 'kurt_approx',\n", "        'mean_detail1', 'var_detail1', 'skew_detail1', 'kurt_detail1',\n", "        'mean_detail2', 'var_detail2', 'skew_detail2', 'kurt_detail2',\n", "        'mean_detail3', 'var_detail3', 'skew_detail3', 'kurt_detail3'\n", "    ])\n", "    return feature_df\n", "\n", "# Step 3: Standardize the features using StandardScaler\n", "def standardize_features(healthy_features, unhealthy_features):\n", "    scaler = StandardScaler()\n", "    healthy_features_scaled = scaler.fit_transform(healthy_features)\n", "    unhealthy_features_scaled = scaler.transform(unhealthy_features)  # Use the same scaler for both datasets\n", "    return healthy_features_scaled, unhealthy_features_scaled\n", "\n", "# Step 4: Train the anomaly detection model using Isolation Forest\n", "def build_anomaly_detection_model(healthy_features_scaled, unhealthy_features_scaled):\n", "    model = IsolationForest(contamination=0.1)  # Assuming 10% anomalies in the data\n", "    # Fit the model using both healthy and unhealthy data\n", "    combined_features = np.concatenate([healthy_features_scaled, unhealthy_features_scaled], axis=0)\n", "    model.fit(combined_features)\n", "    return model\n", "\n", "# Step 5: Visualize the results\n", "def visualize_data(features, labels, title=\"Anomaly Detection Visualization\"):\n", "    pca = PCA(n_components=2)\n", "    reduced_features = pca.fit_transform(features)\n", "    plt.figure(figsize=(10, 6))\n", "    plt.scatter(reduced_features[:, 0], reduced_features[:, 1], c=labels, cmap='coolwarm', label=\"Data Points\")\n", "    plt.title(title)\n", "    plt.xlabel('Principal Component 1')\n", "    plt.<PERSON><PERSON><PERSON>('Principal Component 2')\n", "    plt.legend()\n", "    plt.show()\n", "\n", "# Step 6: Evaluate the model performance (Optional)\n", "def evaluate_model(predictions):\n", "    cm = confusion_matrix(predictions, np.ones(len(predictions)))  # Assuming 1 means anomaly\n", "    print(\"Confusion Matrix:\")\n", "    print(cm)\n", "    print(\"Classification Report:\")\n", "    print(classification_report(predictions, np.ones(len(predictions))))\n", "\n", "healthy_folder_path = 'heathy_small'  # Replace with your healthy data path\n", "unhealthy_folder_path = 'unhealthy_small'  # Replace with your unhealthy data path\n", "\n", "# Step 1: Load data\n", "healthy_data, unhealthy_data = load_and_preprocess_data(healthy_folder_path, unhealthy_folder_path)\n", "\n", "# Step 2: Extract features\n", "healthy_features = extract_features(healthy_data)\n", "unhealthy_features = extract_features(unhealthy_data)\n", "\n", "# Step 3: Standardize features\n", "healthy_features_scaled, unhealthy_features_scaled = standardize_features(healthy_features, unhealthy_features)\n", "\n", "# Step 4: Train the anomaly detection model\n", "model = build_anomaly_detection_model(healthy_features_scaled, unhealthy_features_scaled)\n", "\n", "# Step 5: Predict anomalies\n", "healthy_predictions = model.predict(healthy_features_scaled)\n", "unhealthy_predictions = model.predict(unhealthy_features_scaled)\n", "\n", "# Visualize the results\n", "visualize_data(healthy_features_scaled, healthy_predictions, title=\"Healthy Data Anomaly Detection\")\n", "visualize_data(unhealthy_features_scaled, unhealthy_predictions, title=\"Unhealthy Data Anomaly Detection\")\n", "\n", "# Evaluate the model if labeled data is available\n", "# evaluate_model(unhealthy_predictions)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading file: heathy_small/traindata_201911/traindata_20191027_171117.xlsx\n", "Loading file: heathy_small/traindata_201911/traindata_20191028_081019.xlsx\n", "Loading file: heathy_small/traindata_201911/traindata_20191025_160516.xlsx\n"]}, {"ename": "ValueError", "evalue": "setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (3,) + inhomogeneous part.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[3], line 125\u001b[0m\n\u001b[1;32m    123\u001b[0m \u001b[38;5;66;03m# Execute the main function\u001b[39;00m\n\u001b[1;32m    124\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;18m__name__\u001b[39m \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m__main__\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m--> 125\u001b[0m     \u001b[43mmain\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[3], line 74\u001b[0m, in \u001b[0;36mmain\u001b[0;34m()\u001b[0m\n\u001b[1;32m     71\u001b[0m unhealthy_folder_path \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124munhealthy_small\u001b[39m\u001b[38;5;124m'\u001b[39m  \n\u001b[1;32m     73\u001b[0m \u001b[38;5;66;03m# Load and preprocess data\u001b[39;00m\n\u001b[0;32m---> 74\u001b[0m healthy_data, healthy_labels \u001b[38;5;241m=\u001b[39m \u001b[43mload_data_from_folder\u001b[49m\u001b[43m(\u001b[49m\u001b[43mhealthy_folder_path\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlabel\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m     75\u001b[0m unhealthy_data, unhealthy_labels \u001b[38;5;241m=\u001b[39m load_data_from_folder(unhealthy_folder_path, label\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m1\u001b[39m)\n\u001b[1;32m     77\u001b[0m \u001b[38;5;66;03m# Combine healthy and unhealthy data\u001b[39;00m\n", "Cell \u001b[0;32mIn[3], line 30\u001b[0m, in \u001b[0;36mload_data_from_folder\u001b[0;34m(folder_path, label)\u001b[0m\n\u001b[1;32m     27\u001b[0m                 data\u001b[38;5;241m.\u001b[39mappend(features)\n\u001b[1;32m     28\u001b[0m                 labels\u001b[38;5;241m.\u001b[39mappend(label)  \u001b[38;5;66;03m# Assign the label (0 for healthy, 1 for unhealthy)\u001b[39;00m\n\u001b[0;32m---> 30\u001b[0m \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[43mnp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43marray\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m)\u001b[49m, np\u001b[38;5;241m.\u001b[39marray(labels)\n", "\u001b[0;31mValueError\u001b[0m: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (3,) + inhomogeneous part."]}], "source": ["import os\n", "import pandas as pd\n", "import numpy as np\n", "import pywt\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.metrics import accuracy_score, classification_report\n", "\n", "# Step 1: Load data from the folder (only first 6 columns for sensors)\n", "def load_data_from_folder(folder_path, label):\n", "    data = []\n", "    labels = []\n", "    \n", "    for subfolder in os.listdir(folder_path):\n", "        subfolder_path = os.path.join(folder_path, subfolder)\n", "        if os.path.isdir(subfolder_path):\n", "            for file_name in os.listdir(subfolder_path):\n", "                if file_name.endswith('.xlsx'):\n", "                    file_path = os.path.join(subfolder_path, file_name)\n", "                    print(f\"Loading file: {file_path}\")\n", "                    df = pd.read_excel(file_path, usecols=range(6))  # Load only the first 6 columns (sensor data)\n", "                    df.fillna(df.median(), inplace=True)  # Handle missing values by filling with median\n", "                    \n", "                    # Flatten the DataFrame to a single feature vector for each row\n", "                    features = df.values.flatten()\n", "                    data.append(features)\n", "                    labels.append(label)  # Assign the label (0 for healthy, 1 for unhealthy)\n", "    \n", "    return np.array(data), np.array(labels)\n", "\n", "# Step 2: Feature extraction using wavelet decomposition\n", "def extract_wavelet_features(df):\n", "    features = []\n", "    for sensor in df.columns:\n", "        # Perform 3-level wavelet decomposition using Daube<PERSON><PERSON> wavelet (db1)\n", "        coeffs = pywt.wavedec(df[sensor], 'db1', level=3)\n", "        \n", "        # Extract approximation (a3) and detailed (d1, d2, d3) coefficients\n", "        approx, detail1, detail2, detail3 = coeffs\n", "        \n", "        # Calculate statistical features for each coefficient\n", "        feature_vector = [\n", "            np.mean(approx), np.var(approx), np.skew(approx), np.kurtosis(approx),\n", "            np.mean(detail1), np.var(detail1), np.skew(detail1), np.kurtosis(detail1),\n", "            np.mean(detail2), np.var(detail2), np.skew(detail2), np.kurtosis(detail2),\n", "            np.mean(detail3), np.var(detail3), np.skew(detail3), np.kurtosis(detail3)\n", "        ]\n", "        \n", "        features.extend(feature_vector)\n", "    \n", "    return np.array(features)\n", "\n", "# Step 3: Model training (Random Forest Classifier)\n", "def train_classifier(X_train, y_train):\n", "    model = RandomForestClassifier(n_estimators=100, random_state=42)\n", "    model.fit(X_train, y_train)\n", "    return model\n", "\n", "# Step 4: Evaluate the model\n", "def evaluate_model(model, X_test, y_test):\n", "    y_pred = model.predict(X_test)\n", "    print(f\"Accuracy: {accuracy_score(y_test, y_pred)}\")\n", "    print(\"Classification Report:\")\n", "    print(classification_report(y_test, y_pred))\n", "\n", "# Step 5: Main execution\n", "def main():\n", "    # Define file paths for healthy and unhealthy data\n", "    healthy_folder_path = 'heathy_small'  # Replace with your healthy data path\n", "    unhealthy_folder_path = 'unhealthy_small'  \n", "\n", "    # Load and preprocess data\n", "    healthy_data, healthy_labels = load_data_from_folder(healthy_folder_path, label=0)\n", "    unhealthy_data, unhealthy_labels = load_data_from_folder(unhealthy_folder_path, label=1)\n", "\n", "    # Combine healthy and unhealthy data\n", "    data = np.vstack((healthy_data, unhealthy_data))\n", "    labels = np.concatenate((healthy_labels, unhealthy_labels))\n", "\n", "    # Convert data to DataFrame for feature extraction (wavelet)\n", "    data_df = pd.DataFrame(data)\n", "\n", "    # Step 2: Feature extraction using wavelet decomposition\n", "    features = []\n", "    for index, row in data_df.iterrows():\n", "        feature_vector = extract_wavelet_features(row)\n", "        features.append(feature_vector)\n", "\n", "    features = np.array(features)\n", "\n", "    # Step 3: Standardize the features\n", "    scaler = StandardScaler()\n", "    features_scaled = scaler.fit_transform(features)\n", "\n", "    # Step 4: Split into train and test sets\n", "    X_train, X_test, y_train, y_test = train_test_split(features_scaled, labels, test_size=0.3, random_state=42)\n", "\n", "    # Step 5: Train the model\n", "    model = train_classifier(X_train, y_train)\n", "\n", "    # Step 6: Evaluate the model\n", "    evaluate_model(model, X_test, y_test)\n", "\n", "    # Example prediction on new data (replace with actual input data for real-time prediction)\n", "    # Let's assume new_sensor_data is a 2D array of shape (1, 6) representing 6 sensor values\n", "    new_sensor_data = np.array([[0.2, 0.1, -0.3, 0.5, -0.2, 0.4]])  # Example values\n", "    new_sensor_data_df = pd.DataFrame(new_sensor_data)\n", "\n", "    # Feature extraction for new data\n", "    new_sensor_features = []\n", "    for index, row in new_sensor_data_df.iterrows():\n", "        feature_vector = extract_wavelet_features(row)\n", "        new_sensor_features.append(feature_vector)\n", "\n", "    new_sensor_features = np.array(new_sensor_features)\n", "    new_sensor_features_scaled = scaler.transform(new_sensor_features)\n", "\n", "    # Make prediction\n", "    prediction = model.predict(new_sensor_features_scaled)\n", "    print(f\"Prediction for the new data: {'Healthy' if prediction[0] == 0 else 'Unhealthy'}\")\n", "\n", "# Execute the main function\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading file: heathy_small/traindata_201911/traindata_20191027_171117.xlsx\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/dl-env/lib/python3.10/site-packages/pywt/_multilevel.py:43: UserWarning: Level value of 3 is too high: all coefficients will experience boundary effects.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading file: heathy_small/traindata_201911/traindata_20191028_081019.xlsx\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/dl-env/lib/python3.10/site-packages/pywt/_multilevel.py:43: UserWarning: Level value of 3 is too high: all coefficients will experience boundary effects.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading file: heathy_small/traindata_201911/traindata_20191025_160516.xlsx\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/dl-env/lib/python3.10/site-packages/pywt/_multilevel.py:43: UserWarning: Level value of 3 is too high: all coefficients will experience boundary effects.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading file: unhealthy_small/traindata_201903/traindata_20190301_080625.xlsx\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/dl-env/lib/python3.10/site-packages/pywt/_multilevel.py:43: UserWarning: Level value of 3 is too high: all coefficients will experience boundary effects.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading file: unhealthy_small/traindata_201903/traindata_20190321_170152.xlsx\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/dl-env/lib/python3.10/site-packages/pywt/_multilevel.py:43: UserWarning: Level value of 3 is too high: all coefficients will experience boundary effects.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading file: unhealthy_small/traindata_201903/traindata_20190306_170441.xlsx\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/dl-env/lib/python3.10/site-packages/pywt/_multilevel.py:43: UserWarning: Level value of 3 is too high: all coefficients will experience boundary effects.\n", "  warnings.warn(\n", "/Users/<USER>/dl-env/lib/python3.10/site-packages/sklearn/utils/extmath.py:1137: RuntimeWarning: invalid value encountered in divide\n", "  updated_mean = (last_sum + new_sum) / updated_sample_count\n", "/Users/<USER>/dl-env/lib/python3.10/site-packages/sklearn/utils/extmath.py:1142: RuntimeWarning: invalid value encountered in divide\n", "  T = new_sum / new_sample_count\n", "/Users/<USER>/dl-env/lib/python3.10/site-packages/sklearn/utils/extmath.py:1162: RuntimeWarning: invalid value encountered in divide\n", "  new_unnormalized_variance -= correction**2 / new_sample_count\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Accuracy: 0.6992908473131518\n", "Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.68      0.65      0.66     38269\n", "           1       0.72      0.74      0.73     46198\n", "\n", "    accuracy                           0.70     84467\n", "   macro avg       0.70      0.69      0.70     84467\n", "weighted avg       0.70      0.70      0.70     84467\n", "\n"]}, {"ename": "ValueError", "evalue": "X has 6 features, but StandardScaler is expecting 16 features as input.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[7], line 107\u001b[0m\n\u001b[1;32m    105\u001b[0m \u001b[38;5;66;03m# Execute the main function\u001b[39;00m\n\u001b[1;32m    106\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;18m__name__\u001b[39m \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m__main__\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m--> 107\u001b[0m     \u001b[43mmain\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[7], line 99\u001b[0m, in \u001b[0;36mmain\u001b[0;34m()\u001b[0m\n\u001b[1;32m     96\u001b[0m \u001b[38;5;66;03m# Example prediction on new data (replace with actual input data for real-time prediction)\u001b[39;00m\n\u001b[1;32m     97\u001b[0m \u001b[38;5;66;03m# Let's assume new_sensor_data is a 2D array of shape (1, 6) representing 6 sensor values\u001b[39;00m\n\u001b[1;32m     98\u001b[0m new_sensor_data \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39marray([[\u001b[38;5;241m0.2\u001b[39m, \u001b[38;5;241m0.1\u001b[39m, \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m0.3\u001b[39m, \u001b[38;5;241m0.5\u001b[39m, \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m0.2\u001b[39m, \u001b[38;5;241m0.4\u001b[39m]])  \u001b[38;5;66;03m# Example values\u001b[39;00m\n\u001b[0;32m---> 99\u001b[0m new_sensor_data_scaled \u001b[38;5;241m=\u001b[39m \u001b[43mscaler\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtransform\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnew_sensor_data\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    101\u001b[0m \u001b[38;5;66;03m# Make prediction\u001b[39;00m\n\u001b[1;32m    102\u001b[0m prediction \u001b[38;5;241m=\u001b[39m model\u001b[38;5;241m.\u001b[39mpredict(new_sensor_data_scaled)\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/sklearn/utils/_set_output.py:316\u001b[0m, in \u001b[0;36m_wrap_method_output.<locals>.wrapped\u001b[0;34m(self, X, *args, **kwargs)\u001b[0m\n\u001b[1;32m    314\u001b[0m \u001b[38;5;129m@wraps\u001b[39m(f)\n\u001b[1;32m    315\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mwrapped\u001b[39m(\u001b[38;5;28mself\u001b[39m, X, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[0;32m--> 316\u001b[0m     data_to_wrap \u001b[38;5;241m=\u001b[39m \u001b[43mf\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    317\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(data_to_wrap, \u001b[38;5;28mtuple\u001b[39m):\n\u001b[1;32m    318\u001b[0m         \u001b[38;5;66;03m# only wrap the first output for cross decomposition\u001b[39;00m\n\u001b[1;32m    319\u001b[0m         return_tuple \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m    320\u001b[0m             _wrap_data_with_container(method, data_to_wrap[\u001b[38;5;241m0\u001b[39m], X, \u001b[38;5;28mself\u001b[39m),\n\u001b[1;32m    321\u001b[0m             \u001b[38;5;241m*\u001b[39mdata_to_wrap[\u001b[38;5;241m1\u001b[39m:],\n\u001b[1;32m    322\u001b[0m         )\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/sklearn/preprocessing/_data.py:1045\u001b[0m, in \u001b[0;36mStandardScaler.transform\u001b[0;34m(self, X, copy)\u001b[0m\n\u001b[1;32m   1042\u001b[0m check_is_fitted(\u001b[38;5;28mself\u001b[39m)\n\u001b[1;32m   1044\u001b[0m copy \u001b[38;5;241m=\u001b[39m copy \u001b[38;5;28;01mif\u001b[39;00m copy \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mN<PERSON>\u001b[39;00m \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcopy\n\u001b[0;32m-> 1045\u001b[0m X \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_validate_data\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1046\u001b[0m \u001b[43m    \u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1047\u001b[0m \u001b[43m    \u001b[49m\u001b[43mreset\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m   1048\u001b[0m \u001b[43m    \u001b[49m\u001b[43maccept_sparse\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcsr\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1049\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcopy\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcopy\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1050\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdtype\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mFLOAT_DTYPES\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1051\u001b[0m \u001b[43m    \u001b[49m\u001b[43mforce_writeable\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m   1052\u001b[0m \u001b[43m    \u001b[49m\u001b[43mforce_all_finite\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mallow-nan\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1053\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1055\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m sparse\u001b[38;5;241m.\u001b[39missparse(X):\n\u001b[1;32m   1056\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mwith_mean:\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/sklearn/base.py:654\u001b[0m, in \u001b[0;36mBaseEstimator._validate_data\u001b[0;34m(self, X, y, reset, validate_separately, cast_to_ndarray, **check_params)\u001b[0m\n\u001b[1;32m    651\u001b[0m     out \u001b[38;5;241m=\u001b[39m X, y\n\u001b[1;32m    653\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m no_val_X \u001b[38;5;129;01mand\u001b[39;00m check_params\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mensure_2d\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mTrue\u001b[39;00m):\n\u001b[0;32m--> 654\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_check_n_features\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mreset\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mreset\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    656\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m out\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/sklearn/base.py:443\u001b[0m, in \u001b[0;36mBaseEstimator._check_n_features\u001b[0;34m(self, X, reset)\u001b[0m\n\u001b[1;32m    440\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m\n\u001b[1;32m    442\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m n_features \u001b[38;5;241m!=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mn_features_in_:\n\u001b[0;32m--> 443\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m    444\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mX has \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mn_features\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m features, but \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__class__\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    445\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mis expecting \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mn_features_in_\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m features as input.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    446\u001b[0m     )\n", "\u001b[0;31mValueError\u001b[0m: X has 6 features, but StandardScaler is expecting 16 features as input."]}], "source": ["import os\n", "import pandas as pd\n", "import numpy as np\n", "import pywt\n", "from scipy.stats import skew, kurtosis  # Import skew and kurtosis from scipy.stats\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.metrics import accuracy_score, classification_report\n", "\n", "# Step 1: Load data from the folder (only first 6 columns for sensors)\n", "def load_data_from_folder(folder_path, label):\n", "    data = []\n", "    labels = []\n", "    \n", "    for subfolder in os.listdir(folder_path):\n", "        subfolder_path = os.path.join(folder_path, subfolder)\n", "        if os.path.isdir(subfolder_path):\n", "            for file_name in os.listdir(subfolder_path):\n", "                if file_name.endswith('.xlsx'):\n", "                    file_path = os.path.join(subfolder_path, file_name)\n", "                    print(f\"Loading file: {file_path}\")\n", "                    # Load only the first 6 columns (sensor data)\n", "                    df = pd.read_excel(file_path, usecols=range(6))  \n", "                    df.fillna(df.median(), inplace=True)  # Handle missing values by filling with median\n", "                    \n", "                    # Ensure that each row has a consistent number of features\n", "                    features = []\n", "                    for index, row in df.iterrows():\n", "                        feature_vector = extract_wavelet_features(row)\n", "                        features.append(feature_vector)\n", "                    \n", "                    data.extend(features)\n", "                    labels.extend([label] * len(features))  # Assign the same label for all rows in a file\n", "    \n", "    return np.array(data), np.array(labels)\n", "\n", "# Step 2: Feature extraction using wavelet decomposition (applied to the whole row of sensor data)\n", "def extract_wavelet_features(row):\n", "    # Perform 3-level wavelet decomposition using Daube<PERSON><PERSON> wavelet (db1) on the entire row\n", "    coeffs = pywt.wavedec(row, 'db1', level=3)\n", "    \n", "    # Extract approximation (a3) and detailed (d1, d2, d3) coefficients\n", "    approx, detail1, detail2, detail3 = coeffs\n", "    \n", "    # Calculate statistical features for each coefficient\n", "    feature_vector = [\n", "        np.mean(approx), np.var(approx), skew(approx), kurtosis(approx),\n", "        np.mean(detail1), np.var(detail1), skew(detail1), kurtosis(detail1),\n", "        np.mean(detail2), np.var(detail2), skew(detail2), kurtosis(detail2),\n", "        np.mean(detail3), np.var(detail3), skew(detail3), kurtosis(detail3)\n", "    ]\n", "    \n", "    return np.array(feature_vector)\n", "\n", "# Step 3: Model training (Random Forest Classifier)\n", "def train_classifier(X_train, y_train):\n", "    model = RandomForestClassifier(n_estimators=100, random_state=42)\n", "    model.fit(X_train, y_train)\n", "    return model\n", "\n", "# Step 4: Evaluate the model\n", "def evaluate_model(model, X_test, y_test):\n", "    y_pred = model.predict(X_test)\n", "    print(f\"Accuracy: {accuracy_score(y_test, y_pred)}\")\n", "    print(\"Classification Report:\")\n", "    print(classification_report(y_test, y_pred))\n", "\n", "# Step 5: Main execution\n", "def main():\n", "    # Define file paths for healthy and unhealthy data\n", "    healthy_folder_path = 'heathy_small'  # Replace with your healthy data path\n", "    unhealthy_folder_path = 'unhealthy_small'  # Replace with your unhealthy data path\n", "\n", "    # Load and preprocess data\n", "    healthy_data, healthy_labels = load_data_from_folder(healthy_folder_path, label=0)\n", "    unhealthy_data, unhealthy_labels = load_data_from_folder(unhealthy_folder_path, label=1)\n", "\n", "    # Combine healthy and unhealthy data\n", "    data = np.vstack((healthy_data, unhealthy_data))\n", "    labels = np.concatenate((healthy_labels, unhealthy_labels))\n", "\n", "    # Step 3: Standardize the features\n", "    scaler = StandardScaler()\n", "    data_scaled = scaler.fit_transform(data)\n", "\n", "    # Step 4: Split into train and test sets\n", "    X_train, X_test, y_train, y_test = train_test_split(data_scaled, labels, test_size=0.3, random_state=42)\n", "\n", "    # Step 5: Train the model\n", "    model = train_classifier(X_train, y_train)\n", "\n", "    # Step 6: Evaluate the model\n", "    evaluate_model(model, X_test, y_test)\n", "\n", "    # Example prediction on new data (replace with actual input data for real-time prediction)\n", "    # Let's assume new_sensor_data is a 2D array of shape (1, 6) representing 6 sensor values\n", "    new_sensor_data = np.array([[0.2, 0.1, -0.3, 0.5, -0.2, 0.4]])  # Example values\n", "    new_sensor_data_scaled = scaler.transform(new_sensor_data)\n", "\n", "    # Make prediction\n", "    prediction = model.predict(new_sensor_data_scaled)\n", "    print(f\"Prediction for the new data: {'Healthy' if prediction[0] == 0 else 'Unhealthy'}\")\n", "\n", "# Execute the main function\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading file: heathy_small/traindata_201911/traindata_20191027_171117.xlsx\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/dl-env/lib/python3.10/site-packages/pywt/_multilevel.py:43: UserWarning: Level value of 3 is too high: all coefficients will experience boundary effects.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading file: heathy_small/traindata_201911/traindata_20191028_081019.xlsx\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/dl-env/lib/python3.10/site-packages/pywt/_multilevel.py:43: UserWarning: Level value of 3 is too high: all coefficients will experience boundary effects.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading file: heathy_small/traindata_201911/traindata_20191025_160516.xlsx\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/dl-env/lib/python3.10/site-packages/pywt/_multilevel.py:43: UserWarning: Level value of 3 is too high: all coefficients will experience boundary effects.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading file: unhealthy_small/traindata_201903/traindata_20190301_080625.xlsx\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/dl-env/lib/python3.10/site-packages/pywt/_multilevel.py:43: UserWarning: Level value of 3 is too high: all coefficients will experience boundary effects.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading file: unhealthy_small/traindata_201903/traindata_20190321_170152.xlsx\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/dl-env/lib/python3.10/site-packages/pywt/_multilevel.py:43: UserWarning: Level value of 3 is too high: all coefficients will experience boundary effects.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading file: unhealthy_small/traindata_201903/traindata_20190306_170441.xlsx\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/dl-env/lib/python3.10/site-packages/pywt/_multilevel.py:43: UserWarning: Level value of 3 is too high: all coefficients will experience boundary effects.\n", "  warnings.warn(\n", "/Users/<USER>/dl-env/lib/python3.10/site-packages/sklearn/utils/extmath.py:1137: RuntimeWarning: invalid value encountered in divide\n", "  updated_mean = (last_sum + new_sum) / updated_sample_count\n", "/Users/<USER>/dl-env/lib/python3.10/site-packages/sklearn/utils/extmath.py:1142: RuntimeWarning: invalid value encountered in divide\n", "  T = new_sum / new_sample_count\n", "/Users/<USER>/dl-env/lib/python3.10/site-packages/sklearn/utils/extmath.py:1162: RuntimeWarning: invalid value encountered in divide\n", "  new_unnormalized_variance -= correction**2 / new_sample_count\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Accuracy: 0.6992908473131518\n", "Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.68      0.65      0.66     38269\n", "           1       0.72      0.74      0.73     46198\n", "\n", "    accuracy                           0.70     84467\n", "   macro avg       0.70      0.69      0.70     84467\n", "weighted avg       0.70      0.70      0.70     84467\n", "\n", "Prediction for the new data: Unhealthy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/dl-env/lib/python3.10/site-packages/pywt/_multilevel.py:43: UserWarning: Level value of 3 is too high: all coefficients will experience boundary effects.\n", "  warnings.warn(\n"]}], "source": ["import os\n", "import pandas as pd\n", "import numpy as np\n", "import pywt\n", "from scipy.stats import skew, kurtosis  # Import skew and kurtosis from scipy.stats\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.metrics import accuracy_score, classification_report\n", "\n", "# Step 1: Load data from the folder (only first 6 columns for sensors)\n", "def load_data_from_folder(folder_path, label):\n", "    data = []\n", "    labels = []\n", "    \n", "    for subfolder in os.listdir(folder_path):\n", "        subfolder_path = os.path.join(folder_path, subfolder)\n", "        if os.path.isdir(subfolder_path):\n", "            for file_name in os.listdir(subfolder_path):\n", "                if file_name.endswith('.xlsx'):\n", "                    file_path = os.path.join(subfolder_path, file_name)\n", "                    print(f\"Loading file: {file_path}\")\n", "                    # Load only the first 6 columns (sensor data)\n", "                    df = pd.read_excel(file_path, usecols=range(6))  \n", "                    df.fillna(df.median(), inplace=True)  # Handle missing values by filling with median\n", "                    \n", "                    # Ensure that each row has a consistent number of features\n", "                    features = []\n", "                    for index, row in df.iterrows():\n", "                        feature_vector = extract_wavelet_features(row)\n", "                        features.append(feature_vector)\n", "                    \n", "                    data.extend(features)\n", "                    labels.extend([label] * len(features))  # Assign the same label for all rows in a file\n", "    \n", "    return np.array(data), np.array(labels)\n", "\n", "# Step 2: Feature extraction using wavelet decomposition (applied to the whole row of sensor data)\n", "def extract_wavelet_features(row):\n", "    # Perform 3-level wavelet decomposition using Daube<PERSON><PERSON> wavelet (db1) on the entire row\n", "    coeffs = pywt.wavedec(row, 'db1', level=3)\n", "    \n", "    # Extract approximation (a3) and detailed (d1, d2, d3) coefficients\n", "    approx, detail1, detail2, detail3 = coeffs\n", "    \n", "    # Calculate statistical features for each coefficient\n", "    feature_vector = [\n", "        np.mean(approx), np.var(approx), skew(approx), kurtosis(approx),\n", "        np.mean(detail1), np.var(detail1), skew(detail1), kurtosis(detail1),\n", "        np.mean(detail2), np.var(detail2), skew(detail2), kurtosis(detail2),\n", "        np.mean(detail3), np.var(detail3), skew(detail3), kurtosis(detail3)\n", "    ]\n", "    \n", "    return np.array(feature_vector)\n", "\n", "# Step 3: Model training (Random Forest Classifier)\n", "def train_classifier(X_train, y_train):\n", "    model = RandomForestClassifier(n_estimators=100, random_state=42)\n", "    model.fit(X_train, y_train)\n", "    return model\n", "\n", "# Step 4: Evaluate the model\n", "def evaluate_model(model, X_test, y_test):\n", "    y_pred = model.predict(X_test)\n", "    print(f\"Accuracy: {accuracy_score(y_test, y_pred)}\")\n", "    print(\"Classification Report:\")\n", "    print(classification_report(y_test, y_pred))\n", "\n", "# Step 5: Main execution\n", "def main():\n", "    # Define file paths for healthy and unhealthy data\n", "    healthy_folder_path = 'heathy_small'  # Replace with your healthy data path\n", "    unhealthy_folder_path = 'unhealthy_small'  # Replace with your unhealthy data path\n", "\n", "    # Load and preprocess data\n", "    healthy_data, healthy_labels = load_data_from_folder(healthy_folder_path, label=0)\n", "    unhealthy_data, unhealthy_labels = load_data_from_folder(unhealthy_folder_path, label=1)\n", "\n", "    # Combine healthy and unhealthy data\n", "    data = np.vstack((healthy_data, unhealthy_data))\n", "    labels = np.concatenate((healthy_labels, unhealthy_labels))\n", "\n", "    # Step 3: Standardize the features\n", "    scaler = StandardScaler()\n", "    data_scaled = scaler.fit_transform(data)\n", "\n", "    # Step 4: Split into train and test sets\n", "    X_train, X_test, y_train, y_test = train_test_split(data_scaled, labels, test_size=0.3, random_state=42)\n", "\n", "    # Step 5: Train the model\n", "    model = train_classifier(X_train, y_train)\n", "\n", "    # Step 6: Evaluate the model\n", "    evaluate_model(model, X_test, y_test)\n", "\n", "    # Example prediction on new data (replace with actual input data for real-time prediction)\n", "    # Let's assume new_sensor_data is a 2D array of shape (1, 6) representing 6 sensor values\n", "    new_sensor_data = np.array([[0.2, 0.1, -0.3, 0.5, -0.2, 0.4]])  # Example values\n", "    \n", "    # Apply wavelet feature extraction to new sensor data\n", "    new_sensor_features = []\n", "    for index, row in pd.DataFrame(new_sensor_data).iterrows():\n", "        feature_vector = extract_wavelet_features(row)\n", "        new_sensor_features.append(feature_vector)\n", "\n", "    new_sensor_features = np.array(new_sensor_features)\n", "    \n", "    # Scale the features before prediction\n", "    new_sensor_features_scaled = scaler.transform(new_sensor_features)\n", "\n", "    # Make prediction\n", "    prediction = model.predict(new_sensor_features_scaled)\n", "    print(f\"Prediction for the new data: {'Healthy' if prediction[0] == 0 else 'Unhealthy'}\")\n", "\n", "# Execute the main function\n", "if __name__ == \"__main__\":\n", "    main()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "dl-env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 2}