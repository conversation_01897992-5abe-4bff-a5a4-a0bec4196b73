{"cells": [{"cell_type": "code", "execution_count": 3, "id": "881b218b", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "\n", "# Load sample files (replace with your file paths)\n", "healthy_file = \"healthy/traindata_20191030_170103.xlsx\"\n", "unhealthy_file = \"unhealthy/traindata_201904/traindata_20190428_161334.xlsx\"\n", "healthy_data = pd.read_excel(healthy_file).iloc[:, :6]  # First 6 columns (sensors)\n", "unhealthy_data = pd.read_excel(unhealthy_file).iloc[:, :6]\n", "\n", "# Sampling parameters\n", "fs = 1651  # Hz\n", "t = np.arange(0, min(len(healthy_data), 41275)) / fs  # Up to 25s\n", "\n", "# Plot\n", "fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8), sharex=True)\n", "for i in range(6):\n", "    ax1.plot(t, healthy_data.iloc[:len(t), i], label=f\"Sensor {i+1}\")\n", "    ax2.plot(t, unhealthy_data.iloc[:len(t), i], label=f\"Sensor {i+1}\")\n", "ax1.set_title(\"Healthy Bridge Vibrations\")\n", "ax2.set_title(\"Unhealthy Bridge Vibrations\")\n", "ax1.set_ylabel(\"Acceleration (m/s²)\")\n", "ax2.set_ylabel(\"Acceleration (m/s²)\")\n", "ax2.set_xlabel(\"Time (s)\")\n", "ax1.legend()\n", "ax2.legend()\n", "plt.tight_layout()\n", "plt.savefig(\"time_series_plot.png\", dpi=300)\n", "plt.close()"]}, {"cell_type": "code", "execution_count": 4, "id": "4967552b", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "plt.figure(figsize=(10, 4))\n", "# Simplified bridge (rectangle for deck, triangles for supports)\n", "plt.plot([0, 10], [2, 2], 'k-', lw=5, label='Bridge Deck')  # Deck\n", "plt.plot([0, 2], [2, 0], 'k-')  # Left support\n", "plt.plot([2, 4], [0, 2], 'k-')\n", "plt.plot([6, 8], [2, 0], 'k-')  # Right support\n", "plt.plot([8, 10], [0, 2], 'k-')\n", "# Sensor positions\n", "sensors = [(1, 2.2, 'S1'), (3, 2.2, 'S2'), (4, 2.2, 'S3'), (6, 2.2, 'S4'), (7, 2.2, 'S5'), (9, 2.2, 'S6')]\n", "for x, y, label in sensors:\n", "    plt.plot(x, y, 'ro', markersize=10)\n", "    plt.text(x, y+0.2, label, fontsize=12, ha='center')\n", "plt.title(\"Sensor Placement on Indian Bridge\")\n", "plt.xlabel(\"Bridge Length (m)\")\n", "plt.ylabel(\"Height (m)\")\n", "plt.text(5, 1, \"Traffic & Monsoon Loads\", ha='center', fontsize=10)\n", "plt.axis('equal')\n", "plt.legend()\n", "plt.savefig(\"sensor_placement.png\", dpi=300)\n", "plt.close()"]}, {"cell_type": "code", "execution_count": 8, "id": "ffa0da94", "metadata": {}, "outputs": [], "source": ["from PIL import Image\n", "\n", "# Load and resize image\n", "img = Image.open(\"/Users/<USER>/Documents/bridge_sensor/data-aquisation.png\")\n", "img = img.resize((1050, int(1050 * img.height / img.width)), Image.LANCZOS)\n", "\n", "# Save with compression\n", "img.save(\"data-acquisition_compressed.png\", optimize=True, quality=85)"]}, {"cell_type": "code", "execution_count": null, "id": "76aec0c6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "dl-env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 5}