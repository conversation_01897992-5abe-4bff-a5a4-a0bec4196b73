{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["scikit-learn version: 1.6.1\n", "xgboost version: 3.0.0\n", "\n", "Loading healthy data from /Users/<USER>/Documents/bridge_sensor/healthy...\n", "Error: Directory /Users/<USER>/Documents/bridge_sensor/healthy does not exist.\n", "\n", "Loading unhealthy data from /Users/<USER>/Documents/bridge_sensor/unhealthy...\n", "Error: Directory /Users/<USER>/Documents/bridge_sensor/unhealthy does not exist.\n", "Error: No data loaded from either healthy or unhealthy folders.\n"]}], "source": ["import os\n", "import pandas as pd\n", "import numpy as np\n", "from scipy.fft import fft\n", "from scipy.stats import kurtosis, skew, entropy\n", "from sklearn.model_selection import train_test_split, RandomizedSearchCV\n", "from sklearn.preprocessing import StandardScaler\n", "from xgboost import XGBClassifier\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc\n", "import sklearn\n", "import xgboost\n", "import warnings\n", "\n", "# Suppress warnings for cleaner output\n", "warnings.filterwarnings(\"ignore\", category=FutureWarning)\n", "\n", "# Print library versions for debugging\n", "print(f\"scikit-learn version: {sklearn.__version__}\")\n", "print(f\"xgboost version: {xgboost.__version__}\")\n", "\n", "# Set random seed for reproducibility\n", "np.random.seed(42)\n", "\n", "# 1. Data Loading Function\n", "def load_sensor_data(base_path, condition):\n", "    data_list = []\n", "    folder_path = os.path.join(base_path, condition)\n", "    print(f\"\\nLoading {condition} data from {folder_path}...\")\n", "    \n", "    if not os.path.exists(folder_path):\n", "        print(f\"Error: Directory {folder_path} does not exist.\")\n", "        return data_list\n", "    \n", "    for root, _, files in os.walk(folder_path):\n", "        for file in files:\n", "            if file.endswith('.xlsx'):\n", "                file_path = os.path.join(root, file)\n", "                try:\n", "                    df = pd.read_excel(file_path, usecols=range(6))\n", "                    print(f\"Loaded {file}: Shape {df.shape}\")\n", "                    data_list.append(df)\n", "                except Exception as e:\n", "                    print(f\"Error loading {file}: {str(e)}\")\n", "    \n", "    return data_list\n", "\n", "# 2. Advanced Feature Extraction Function\n", "def extract_features(df, sampling_freq=1651, start_sec=2, duration_sec=20, window_sec=0.5):\n", "    start_idx = start_sec * sampling_freq\n", "    end_idx = start_idx + (duration_sec * sampling_freq)\n", "    \n", "    if start_idx >= len(df):\n", "        print(f\"Warning: Start index exceeds data length for file with {len(df)} samples.\")\n", "        return pd.DataFrame()\n", "    if end_idx > len(df):\n", "        print(f\"Warning: Data truncated from {len(df)} to {end_idx} samples.\")\n", "        end_idx = len(df)\n", "    \n", "    df_chunk = df.iloc[start_idx:end_idx]\n", "    n_samples = len(df_chunk)\n", "    window_size = int(window_sec * sampling_freq)\n", "    n_windows = n_samples // window_size\n", "    \n", "    if n_windows == 0:\n", "        print(f\"Warning: No complete windows available in chunk of {n_samples} samples.\")\n", "        return pd.DataFrame()\n", "    \n", "    features_list = []\n", "    for sensor in range(6):\n", "        sensor_data = df_chunk.iloc[:, sensor]\n", "        sensor_features = []\n", "        \n", "        for i in range(n_windows):\n", "            chunk = sensor_data[i*window_size:(i+1)*window_size]\n", "            \n", "            # Time-domain features\n", "            rms = np.sqrt(np.mean(chunk**2))\n", "            peak_to_peak = np.max(chunk) - np.min(chunk)\n", "            zero_crossings = np.sum(np.diff(np.sign(chunk)) != 0) / 2\n", "            kurt = kurtosis(chunk, nan_policy='omit')\n", "            crest_factor = np.max(np.abs(chunk)) / rms if rms != 0 else 0\n", "            mad = np.mean(np.abs(chunk - np.mean(chunk)))\n", "            skw = skew(chunk, nan_policy='omit')\n", "            energy = np.sum(chunk**2)\n", "            \n", "            # Frequency-domain features\n", "            fft_vals = fft(chunk.to_numpy())\n", "            fft_magnitude = np.abs(fft_vals)[:window_size//2]\n", "            freqs = np.fft.fftfreq(window_size, 1/sampling_freq)[:window_size//2]\n", "            dominant_freq = freqs[np.argmax(fft_magnitude)] if fft_magnitude.size > 0 else 0\n", "            spectral_power = np.sum(fft_magnitude**2) / window_size if fft_magnitude.size > 0 else 0\n", "            fft_magnitude_sorted = np.sort(fft_magnitude)[::-1]\n", "            second_dominant_freq = freqs[np.argsort(fft_magnitude)[-2]] if len(freqs) > 1 else 0\n", "            spectral_entropy = entropy(fft_magnitude / np.sum(fft_magnitude)) if np.sum(fft_magnitude) != 0 else 0\n", "            spectral_centroid = np.sum(freqs * fft_magnitude) / np.sum(fft_magnitude) if np.sum(fft_magnitude) != 0 else 0\n", "            \n", "            feat = {\n", "                f'sensor_{sensor}_rms': rms,\n", "                f'sensor_{sensor}_peak_to_peak': peak_to_peak,\n", "                f'sensor_{sensor}_zero_crossings': zero_crossings,\n", "                f'sensor_{sensor}_kurtosis': kurt,\n", "                f'sensor_{sensor}_crest_factor': crest_factor,\n", "                f'sensor_{sensor}_mad': mad,\n", "                f'sensor_{sensor}_skewness': skw,\n", "                f'sensor_{sensor}_energy': energy,\n", "                f'sensor_{sensor}_dominant_freq': dominant_freq,\n", "                f'sensor_{sensor}_second_dominant_freq': second_dominant_freq,\n", "                f'sensor_{sensor}_spectral_power': spectral_power,\n", "                f'sensor_{sensor}_spectral_entropy': spectral_entropy,\n", "                f'sensor_{sensor}_spectral_centroid': spectral_centroid\n", "            }\n", "            sensor_features.append(feat)\n", "        \n", "        features_list.append(pd.DataFrame(sensor_features))\n", "    \n", "    return pd.concat(features_list, axis=1)\n", "\n", "# 3. Feature Statistics Function\n", "def compute_feature_statistics(X, y, save_path=\"img/feature_stats.txt\"):\n", "    print(\"\\nComputing feature statistics...\")\n", "    \n", "    # Check for missing or invalid values\n", "    nan_count = X.isna().sum().sum()\n", "    inf_count = np.isinf(X).sum().sum()\n", "    print(f\"NaN values in feature matrix: {nan_count}\")\n", "    print(f\"Infinite values in feature matrix: {inf_count}\")\n", "    \n", "    # Replace NaNs with median and infinities with max/min\n", "    X_clean = <PERSON>.fillna(X.median()).replace([np.inf, -np.inf], [X.max(), X.min()])\n", "    \n", "    # Class distribution\n", "    healthy_count = sum(y == 1)\n", "    unhealthy_count = sum(y == 0)\n", "    print(f\"Class distribution: Healthy (1): {healthy_count}, Unhealthy (0): {unhealthy_count}\")\n", "    \n", "    # Summary statistics\n", "    stats = X_clean.describe().T[['mean', 'std', 'min', 'max']]\n", "    stats['variance'] = stats['std'] ** 2\n", "    \n", "    # Group by class\n", "    healthy_stats = X_clean[y == 1].describe().T[['mean', 'std']]\n", "    unhealthy_stats = X_clean[y == 0].describe().T[['mean', 'std']]\n", "    stats = stats.join(healthy_stats.rename(columns={'mean': 'healthy_mean', 'std': 'healthy_std'}))\n", "    stats = stats.join(unhealthy_stats.rename(columns={'mean': 'unhealthy_mean', 'std': 'unhealthy_std'}))\n", "    \n", "    # Save to file\n", "    os.makedirs(os.path.dirname(save_path), exist_ok=True)\n", "    with open(save_path, 'w') as f:\n", "        f.write(\"Feature Statistics\\n\")\n", "        f.write(f\"NaN values: {nan_count}\\n\")\n", "        f.write(f\"Infinite values: {inf_count}\\n\")\n", "        f.write(f\"Class distribution: Healthy: {healthy_count}, Unhealthy: {unhealthy_count}\\n\\n\")\n", "        f.write(stats.to_string())\n", "    \n", "    print(f\"Feature statistics saved to {save_path}\")\n", "    return stats\n", "\n", "# 4. Visualization Functions\n", "def plot_acceleration_time(healthy_data, unhealthy_data, sampling_freq=1651, n_samples=3):\n", "    healthy_sample = healthy_data[:n_samples]\n", "    unhealthy_sample = unhealthy_data[:n_samples]\n", "    max_samples = int(25 * sampling_freq)\n", "    \n", "    for i, df in enumerate(healthy_sample):\n", "        n_samples = min(len(df), max_samples)\n", "        time = np.arange(0, n_samples/sampling_freq, 1/sampling_freq)\n", "        plt.figure(figsize=(3.5, 3))\n", "        df_plot = df.iloc[:n_samples]\n", "        for sensor in range(6):\n", "            plt.plot(time, df_plot.iloc[:, sensor], label=f'Sensor {sensor}', alpha=0.7)\n", "        plt.title(f'<PERSON><PERSON> Sample {i+1}', fontsize=10)\n", "        plt.xlabel('Time (s)', fontsize=8)\n", "        plt.ylabel('Acceleration', fontsize=8)\n", "        plt.legend(fontsize=7)\n", "        plt.grid(True)\n", "        plt.tick_params(axis='both', labelsize=7)\n", "        plt.tight_layout()\n", "        plt.savefig(f\"img/healthy_time_{i+1}.png\", dpi=300, bbox_inches='tight')\n", "        plt.close()\n", "    \n", "    for i, df in enumerate(unhealthy_sample):\n", "        n_samples = min(len(df), max_samples)\n", "        time = np.arange(0, n_samples/sampling_freq, 1/sampling_freq)\n", "        plt.figure(figsize=(3.5, 3))\n", "        df_plot = df.iloc[:n_samples]\n", "        for sensor in range(6):\n", "            plt.plot(time, df_plot.iloc[:, sensor], label=f'Sensor {sensor}', alpha=0.7)\n", "        plt.title(f'Unhealthy Sample {i+1}', fontsize=10)\n", "        plt.xlabel('Time (s)', fontsize=8)\n", "        plt.ylabel('Acceleration', fontsize=8)\n", "        plt.legend(fontsize=7)\n", "        plt.grid(True)\n", "        plt.tick_params(axis='both', labelsize=7)\n", "        plt.tight_layout()\n", "        plt.savefig(f\"img/unhealthy_time_{i+1}.png\", dpi=300, bbox_inches='tight')\n", "        plt.close()\n", "\n", "def plot_feature_distribution(X, y):\n", "    features_df = pd.DataFrame({\n", "        \"RMS\": X[\"sensor_0_rms\"],\n", "        \"Kurtosis\": X[\"sensor_0_kurtosis\"],\n", "        \"Spectral_Entropy\": X[\"sensor_0_spectral_entropy\"],\n", "        \"Class\": [\"Healthy\" if label == 1 else \"Unhealthy\" for label in y]\n", "    })\n", "    \n", "    plt.figure(figsize=(3.5, 3))\n", "    sns.violinplot(data=features_df.melt(id_vars=\"Class\"), x=\"variable\", y=\"value\", hue=\"Class\", split=True)\n", "    plt.xlabel(\"Feature\", fontsize=8)\n", "    plt.ylabel(\"Feature Value\", fontsize=8)\n", "    plt.title(\"Feature Distributions\", fontsize=10)\n", "    plt.tick_params(axis='both', labelsize=7)\n", "    plt.legend(title=\"Class\", fontsize=7)\n", "    plt.tight_layout()\n", "    plt.savefig(\"img/feature_distribution.png\", dpi=300, bbox_inches='tight')\n", "    plt.close()\n", "\n", "def plot_feature_trends(healthy_data, unhealthy_data):\n", "    if not healthy_data or not unhealthy_data:\n", "        print(\"Warning: No data available for feature trends plot.\")\n", "        return\n", "    \n", "    healthy_features = extract_features(healthy_data[0])\n", "    unhealthy_features = extract_features(unhealthy_data[0])\n", "    \n", "    if healthy_features.empty or unhealthy_features.empty:\n", "        print(\"Warning: Feature extraction failed for trends plot.\")\n", "        return\n", "    \n", "    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(3.5, 3), sharex=True)\n", "    windows = range(len(healthy_features))\n", "    ax1.plot(windows, healthy_features[\"sensor_0_rms\"], label=\"Healthy\")\n", "    ax1.plot(windows, unhealthy_features[\"sensor_0_rms\"], label=\"Unhealthy\")\n", "    ax2.plot(windows, healthy_features[\"sensor_0_spectral_power\"], label=\"Healthy\")\n", "    ax2.plot(windows, unhealthy_features[\"sensor_0_spectral_power\"], label=\"Unhealthy\")\n", "    ax1.set_title(\"RMS Over Windows\", fontsize=10)\n", "    ax2.set_title(\"Spectral Power Over Windows\", fontsize=10)\n", "    ax1.set_ylabel(\"RMS\", fontsize=8)\n", "    ax2.set_ylabel(\"Spectral Power\", fontsize=8)\n", "    ax2.set_xlabel(\"Window Index\", fontsize=8)\n", "    ax1.tick_params(axis='both', labelsize=7)\n", "    ax2.tick_params(axis='both', labelsize=7)\n", "    ax1.legend(fontsize=7)\n", "    ax2.legend(fontsize=7)\n", "    plt.tight_layout()\n", "    plt.savefig(\"img/feature_trends.png\", dpi=300, bbox_inches='tight')\n", "    plt.close()\n", "\n", "# 5. Main Processing Pipeline\n", "def main():\n", "    base_path = \"/Users/<USER>/Documents/bridge_sensor/\"\n", "    os.makedirs(\"img\", exist_ok=True)\n", "    \n", "    # Load data\n", "    healthy_data = load_sensor_data(base_path, \"healthy\")\n", "    unhealthy_data = load_sensor_data(base_path, \"unhealthy\")\n", "    \n", "    if not healthy_data and not unhealthy_data:\n", "        print(\"Error: No data loaded from either healthy or unhealthy folders.\")\n", "        return\n", "    \n", "    # Visualize time-series\n", "    print(\"\\nPlotting acceleration-time graphs...\")\n", "    plot_acceleration_time(healthy_data, unhealthy_data, n_samples=3)\n", "    \n", "    # Extract features\n", "    print(\"\\nExtracting features...\")\n", "    X_healthy = []\n", "    y_healthy = []\n", "    for df in healthy_data:\n", "        features = extract_features(df)\n", "        if not features.empty:\n", "            X_healthy.append(features)\n", "            y_healthy.extend([1] * len(features))\n", "    \n", "    X_unhealthy = []\n", "    y_unhealthy = []\n", "    for df in unhealthy_data:\n", "        features = extract_features(df)\n", "        if not features.empty:\n", "            X_unhealthy.append(features)\n", "            y_unhealthy.extend([0] * len(features))\n", "    \n", "    if not X_healthy and not X_unhealthy:\n", "        print(\"Error: No valid data extracted from any files.\")\n", "        return\n", "    \n", "    X = pd.concat(X_healthy + X_unhealthy, axis=0)\n", "    y = np.array(y_healthy + y_unhealthy)\n", "    \n", "    print(f\"\\nFeature matrix shape: {X.shape}\")\n", "    print(f\"Labels shape: {y.shape}\")\n", "    \n", "    # Compute feature statistics\n", "    stats = compute_feature_statistics(X, y)\n", "    \n", "    # Plot feature distribution and trends\n", "    plot_feature_distribution(X, y)\n", "    plot_feature_trends(healthy_data, unhealthy_data)\n", "    \n", "    # Split data\n", "    X_train, X_test, y_train, y_test = train_test_split(\n", "        X, y, test_size=0.2, random_state=42, stratify=y\n", "    )\n", "    \n", "    # Scale features\n", "    scaler = StandardScaler()\n", "    X_train_scaled = scaler.fit_transform(X_train)\n", "    X_test_scaled = scaler.transform(X_test)\n", "    \n", "    # XGBoost with RandomizedSearchCV\n", "    print(\"\\nPerforming RandomizedSearchCV with XGBoost...\")\n", "    xgb = XGBClassifier(\n", "        random_state=42,\n", "        eval_metric='logloss',\n", "        scale_pos_weight=sum(y==0)/sum(y==1)\n", "    )\n", "    \n", "    param_dist = {\n", "        'max_depth': [3, 5, 7, 9],\n", "        'learning_rate': [0.05, 0.1, 0.15, 0.2, 0.3],\n", "        'n_estimators': [100, 200, 300, 400, 500],\n", "        'subsample': [0.6, 0.7, 0.8, 0.9, 1.0],\n", "        'colsample_bytree': [0.6, 0.7, 0.8, 0.9, 1.0],\n", "        'gamma': [0, 0.1, 0.2, 0.3],\n", "        'min_child_weight': [1, 3, 5]\n", "    }\n", "    \n", "    random_search = RandomizedSearchCV(\n", "        estimator=xgb,\n", "        param_distributions=param_dist,\n", "        n_iter=50,\n", "        cv=5,\n", "        scoring='accuracy',\n", "        n_jobs=-1,\n", "        verbose=1,\n", "        random_state=42\n", "    )\n", "    \n", "    try:\n", "        random_search.fit(X_train_scaled, y_train)\n", "    except Exception as e:\n", "        print(f\"Error during RandomizedSearchCV fit: {str(e)}\")\n", "        print(\"Try updating xgboost (`pip install --upgrade xgboost`) or downgrading scikit-learn (`pip install scikit-learn==1.4.2`)\")\n", "        return\n", "    \n", "    print(\"\\nRandomizedSearchCV Results:\")\n", "    print(\"Best parameters:\", random_search.best_params_)\n", "    print(\"Best cross-validation score:\", random_search.best_score_)\n", "    \n", "    # Evaluate the best model\n", "    best_model = random_search.best_estimator_\n", "    y_pred = best_model.predict(X_test_scaled)\n", "    \n", "    print(\"\\nClassification Report:\")\n", "    print(classification_report(y_test, y_pred, target_names=['Unhealthy (0)', 'Healthy (1)']))\n", "    \n", "    # Plot confusion matrix\n", "    plt.figure(figsize=(3.5, 3))\n", "    cm = confusion_matrix(y_test, y_pred)\n", "    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \n", "                xticklabels=['Unhealthy', 'Healthy'], \n", "                yticklabels=['Unhealthy', 'Healthy'])\n", "    plt.title('Confusion Matrix', fontsize=10)\n", "    plt.ylabel('True Label', fontsize=8)\n", "    plt.xlabel('Predicted Label', fontsize=8)\n", "    plt.tick_params(axis='both', labelsize=7)\n", "    plt.tight_layout()\n", "    plt.savefig(\"img/confusion_matrix.png\", dpi=300, bbox_inches='tight')\n", "    plt.close()\n", "    \n", "    # Plot ROC curve\n", "    y_pred_proba = best_model.predict_proba(X_test_scaled)[:, 1]\n", "    fpr, tpr, _ = roc_curve(y_test, y_pred_proba)\n", "    roc_auc = auc(fpr, tpr)\n", "    \n", "    plt.figure(figsize=(3.5, 3))\n", "    plt.plot(fpr, tpr, color='darkorange', lw=1, \n", "             label=f'ROC curve (AUC = {roc_auc:.2f})')\n", "    plt.plot([0, 1], [0, 1], color='navy', lw=1, linestyle='--')\n", "    plt.xlim([0.0, 1.0])\n", "    plt.ylim([0.0, 1.05])\n", "    plt.xlabel('False Positive Rate', fontsize=8)\n", "    plt.ylabel('True Positive Rate', fontsize=8)\n", "    plt.title('ROC Curve', fontsize=10)\n", "    plt.legend(loc=\"lower right\", fontsize=7)\n", "    plt.tick_params(axis='both', labelsize=7)\n", "    plt.tight_layout()\n", "    plt.savefig(\"img/roc_curve.png\", dpi=300, bbox_inches='tight')\n", "    plt.close()\n", "    \n", "    # Plot feature importance\n", "    plt.figure(figsize=(3.5, 3))\n", "    feat_importance = pd.DataFrame({\n", "        'feature': X.columns,\n", "        'importance': best_model.feature_importances_\n", "    })\n", "    feat_importance = feat_importance.sort_values('importance', ascending=False)\n", "    sns.barplot(x='importance', y='feature', data=feat_importance.head(10))\n", "    plt.title('Top 10 Feature Importances', fontsize=10)\n", "    plt.xlabel('Importance Score', fontsize=8)\n", "    plt.ylabel('Feature', fontsize=8)\n", "    plt.tick_params(axis='both', labelsize=7)\n", "    plt.tight_layout()\n", "    plt.savefig(\"img/feature_importance.png\", dpi=300, bbox_inches='tight')\n", "    plt.close()\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: tensorflow in /Users/<USER>/dl-env/lib/python3.10/site-packages (2.16.2)\n", "Requirement already satisfied: numpy in /Users/<USER>/dl-env/lib/python3.10/site-packages (1.26.2)\n", "Requirement already satisfied: pandas in /Users/<USER>/dl-env/lib/python3.10/site-packages (1.3.5)\n", "Requirement already satisfied: matplotlib in /Users/<USER>/dl-env/lib/python3.10/site-packages (3.9.3)\n", "Requirement already satisfied: scikit-learn in /Users/<USER>/dl-env/lib/python3.10/site-packages (1.5.2)\n", "Collecting openpyxl\n", "  Downloading openpyxl-3.1.5-py2.py3-none-any.whl.metadata (2.5 kB)\n", "Requirement already satisfied: absl-py>=1.0.0 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from tensorflow) (2.1.0)\n", "Requirement already satisfied: astunparse>=1.6.0 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from tensorflow) (1.6.3)\n", "Requirement already satisfied: flatbuffers>=23.5.26 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from tensorflow) (24.3.25)\n", "Requirement already satisfied: gast!=0.5.0,!=0.5.1,!=0.5.2,>=0.2.1 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from tensorflow) (0.6.0)\n", "Requirement already satisfied: google-pasta>=0.1.1 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from tensorflow) (0.2.0)\n", "Requirement already satisfied: h5py>=3.10.0 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from tensorflow) (3.12.1)\n", "Requirement already satisfied: libclang>=13.0.0 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from tensorflow) (18.1.1)\n", "Requirement already satisfied: ml-dtypes~=0.3.1 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from tensorflow) (0.3.2)\n", "Requirement already satisfied: opt-einsum>=2.3.2 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from tensorflow) (3.4.0)\n", "Requirement already satisfied: packaging in /Users/<USER>/dl-env/lib/python3.10/site-packages (from tensorflow) (23.2)\n", "Requirement already satisfied: protobuf!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.20.3 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from tensorflow) (4.25.5)\n", "Requirement already satisfied: requests<3,>=2.21.0 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from tensorflow) (2.32.3)\n", "Requirement already satisfied: setuptools in /Users/<USER>/dl-env/lib/python3.10/site-packages (from tensorflow) (75.8.2)\n", "Requirement already satisfied: six>=1.12.0 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from tensorflow) (1.16.0)\n", "Requirement already satisfied: termcolor>=1.1.0 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from tensorflow) (2.5.0)\n", "Requirement already satisfied: typing-extensions>=3.6.6 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from tensorflow) (4.12.2)\n", "Requirement already satisfied: wrapt>=1.11.0 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from tensorflow) (1.17.0)\n", "Requirement already satisfied: grpcio<2.0,>=1.24.3 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from tensorflow) (1.68.1)\n", "Requirement already satisfied: tensorboard<2.17,>=2.16 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from tensorflow) (2.16.2)\n", "Requirement already satisfied: keras>=3.0.0 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from tensorflow) (3.7.0)\n", "Requirement already satisfied: tensorflow-io-gcs-filesystem>=0.23.1 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from tensorflow) (0.37.1)\n", "Requirement already satisfied: python-dateutil>=2.7.3 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from pandas) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2017.3 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from pandas) (2024.2)\n", "Requirement already satisfied: contourpy>=1.0.1 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from matplotlib) (1.3.1)\n", "Requirement already satisfied: cycler>=0.10 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from matplotlib) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from matplotlib) (4.55.1)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from matplotlib) (1.4.7)\n", "Requirement already satisfied: pillow>=8 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from matplotlib) (11.1.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from matplotlib) (3.2.0)\n", "Requirement already satisfied: scipy>=1.6.0 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from scikit-learn) (1.14.1)\n", "Requirement already satisfied: joblib>=1.2.0 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from scikit-learn) (1.4.2)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from scikit-learn) (3.5.0)\n", "Collecting et-xmlfile (from openpyxl)\n", "  Downloading et_xmlfile-2.0.0-py3-none-any.whl.metadata (2.7 kB)\n", "Requirement already satisfied: wheel<1.0,>=0.23.0 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from astunparse>=1.6.0->tensorflow) (0.45.1)\n", "Requirement already satisfied: rich in /Users/<USER>/dl-env/lib/python3.10/site-packages (from keras>=3.0.0->tensorflow) (13.9.4)\n", "Requirement already satisfied: namex in /Users/<USER>/dl-env/lib/python3.10/site-packages (from keras>=3.0.0->tensorflow) (0.0.8)\n", "Requirement already satisfied: optree in /Users/<USER>/dl-env/lib/python3.10/site-packages (from keras>=3.0.0->tensorflow) (0.13.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from requests<3,>=2.21.0->tensorflow) (3.4.0)\n", "Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from requests<3,>=2.21.0->tensorflow) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from requests<3,>=2.21.0->tensorflow) (2.2.3)\n", "Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from requests<3,>=2.21.0->tensorflow) (2024.8.30)\n", "Requirement already satisfied: markdown>=2.6.8 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from tensorboard<2.17,>=2.16->tensorflow) (3.7)\n", "Requirement already satisfied: tensorboard-data-server<0.8.0,>=0.7.0 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from tensorboard<2.17,>=2.16->tensorflow) (0.7.2)\n", "Requirement already satisfied: werkzeug>=1.0.1 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from tensorboard<2.17,>=2.16->tensorflow) (3.1.3)\n", "Requirement already satisfied: MarkupSafe>=2.1.1 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from werkzeug>=1.0.1->tensorboard<2.17,>=2.16->tensorflow) (2.1.5)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from rich->keras>=3.0.0->tensorflow) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from rich->keras>=3.0.0->tensorflow) (2.18.0)\n", "Requirement already satisfied: mdurl~=0.1 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from markdown-it-py>=2.2.0->rich->keras>=3.0.0->tensorflow) (0.1.2)\n", "Downloading openpyxl-3.1.5-py2.py3-none-any.whl (250 kB)\n", "Downloading et_xmlfile-2.0.0-py3-none-any.whl (18 kB)\n", "Installing collected packages: et-xmlfile, openpyxl\n", "Successfully installed et-xmlfile-2.0.0 openpyxl-3.1.5\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m24.3.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install tensorflow numpy pandas matplotlib scikit-learn openpyxl"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: pywavelets in /Users/<USER>/dl-env/lib/python3.10/site-packages (1.7.0)\n", "Requirement already satisfied: numpy<3,>=1.23 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from pywavelets) (1.26.2)\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m24.3.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install pywavelets"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing values check after imputation: \n", "0    0\n", "1    0\n", "2    0\n", "3    0\n", "4    0\n", "5    0\n", "dtype: int64\n"]}, {"data": {"image/png": "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************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Shape of wavelet features: (72,)\n", "Shape of Data Sequences: (22, 50)\n"]}, {"ename": "IndexError", "evalue": "tuple index out of range", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mIndexError\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[5], line 92\u001b[0m\n\u001b[1;32m     89\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m model\n\u001b[1;32m     91\u001b[0m \u001b[38;5;66;03m# LSTM Autoencoder Model\u001b[39;00m\n\u001b[0;32m---> 92\u001b[0m input_shape \u001b[38;5;241m=\u001b[39m (data_sequences\u001b[38;5;241m.\u001b[39mshape[\u001b[38;5;241m1\u001b[39m], \u001b[43mdata_sequences\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mshape\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[43m]\u001b[49m)\n\u001b[1;32m     93\u001b[0m autoencoder \u001b[38;5;241m=\u001b[39m build_lstm_autoencoder(input_shape)\n\u001b[1;32m     95\u001b[0m \u001b[38;5;66;03m# Train the autoencoder on healthy data\u001b[39;00m\n", "\u001b[0;31mIndexError\u001b[0m: tuple index out of range"]}], "source": ["import os\n", "import numpy as np\n", "import pandas as pd\n", "import pywt\n", "from sklearn.preprocessing import StandardScaler\n", "from tensorflow.keras.models import Sequential\n", "from tensorflow.keras.layers import LSTM, Dense, RepeatVector, TimeDistributed\n", "import matplotlib.pyplot as plt\n", "from openpyxl import load_workbook\n", "\n", "# 1. Load Data from Folder\n", "def load_data_from_folder(folder_path):\n", "    data = []\n", "    for subfolder in os.listdir(folder_path):\n", "        subfolder_path = os.path.join(folder_path, subfolder)\n", "        if os.path.isdir(subfolder_path):\n", "            for file in os.listdir(subfolder_path):\n", "                if file.endswith('.xlsx'):\n", "                    file_path = os.path.join(subfolder_path, file)\n", "                    df = pd.read_excel(file_path, header=None)\n", "                    data.append(df.iloc[:, :6])  # Consider only the first 6 columns\n", "    return pd.concat(data, axis=0, ignore_index=True)\n", "\n", "healthy_folder = r'healthy'\n", "unhealthy_folder = r'unhealthy'\n", "\n", "healthy_data = load_data_from_folder(healthy_folder)\n", "unhealthy_data = load_data_from_folder(unhealthy_folder)\n", "\n", "# Combine healthy and unhealthy data for modeling\n", "data = pd.concat([healthy_data, unhealthy_data], axis=0, ignore_index=True)\n", "\n", "# 2. Data Preprocessing\n", "\n", "## Handle missing values (if any)\n", "data = data.fillna(method='ffill').fillna(method='bfill')  # Fill missing values with forward/backward fill\n", "\n", "# Verify that the data is complete after imputation\n", "print(f\"Missing values check after imputation: \\n{data.isnull().sum()}\")\n", "\n", "## Normalize the data (important for time series)\n", "scaler = StandardScaler()\n", "data_scaled = scaler.fit_transform(data)  # Normalize each feature across the time axis\n", "\n", "# Plot a sample to inspect the data\n", "plt.plot(data_scaled[:100, :])  # First 100 data points of the first sensor\n", "plt.title(\"Normalized Sensor Data - Sample\")\n", "plt.xlabel(\"Time\")\n", "plt.ylabel(\"Sensor Value\")\n", "plt.show()\n", "\n", "# 3. Dynamic Feature Extraction using Wavelet Transform\n", "def wavelet_transform(data, wavelet='db1', level=5):\n", "    features = []\n", "    for sensor_data in data.T:  # Iterate over each sensor (column)\n", "        coeffs = pywt.wavedec(sensor_data, wavelet, level=level)\n", "        # Extract approximation and detail coefficients at all levels\n", "        for coeff in coeffs:\n", "            features.append(np.mean(coeff))  # Mean of coefficients as a feature\n", "            features.append(np.std(coeff))  # Standard deviation as a feature\n", "    return np.array(features)\n", "\n", "# Apply wavelet transform to the data\n", "wavelet_features = wavelet_transform(data_scaled)\n", "\n", "# Verify feature extraction dimensions\n", "print(f\"Shape of wavelet features: {wavelet_features.shape}\")\n", "\n", "# 4. Reshape Data for LSTM Autoencoder\n", "def create_sequences(data, seq_length=50):\n", "    sequences = []\n", "    for i in range(len(data) - seq_length):\n", "        sequences.append(data[i:i+seq_length])\n", "    return np.array(sequences)\n", "\n", "# Prepare data for LSTM autoencoder\n", "seq_length = 50\n", "data_sequences = create_sequences(wavelet_features, seq_length)\n", "print(f\"Shape of Data Sequences: {data_sequences.shape}\")\n", "\n", "# 5. Build LSTM Autoencoder Model\n", "def build_lstm_autoencoder(input_shape):\n", "    model = Sequential()\n", "    model.add(LSTM(128, activation='relu', input_shape=input_shape, return_sequences=False))\n", "    model.add(RepeatVector(input_shape[0]))\n", "    model.add(LSTM(128, activation='relu', return_sequences=True))\n", "    model.add(TimeDistributed(Den<PERSON>(input_shape[1])))\n", "    model.compile(optimizer='adam', loss='mse')\n", "    return model\n", "\n", "# LSTM Autoencoder Model\n", "input_shape = (data_sequences.shape[1], data_sequences.shape[2])\n", "autoencoder = build_lstm_autoencoder(input_shape)\n", "\n", "# Train the autoencoder on healthy data\n", "autoencoder.fit(data_sequences[:len(healthy_data)], data_sequences[:len(healthy_data)], epochs=50, batch_size=32, validation_split=0.2, verbose=1)\n", "\n", "# 6. Anomaly Detection: Calculate Reconstruction Errors\n", "def calculate_reconstruction_errors(model, data):\n", "    predictions = model.predict(data)\n", "    errors = np.mean(np.abs(predictions - data), axis=(1, 2))  # Mean absolute error along time and feature axes\n", "    return errors\n", "\n", "# Calculate errors for the whole dataset\n", "errors = calculate_reconstruction_errors(autoencoder, data_sequences)\n", "\n", "# 7. <PERSON> for Anomaly Detection\n", "threshold = np.percentile(errors, 95)  # Use 95th percentile of reconstruction error as threshold for anomaly\n", "print(f\"Anomaly detection threshold: {threshold}\")\n", "\n", "# 8. Detect Anomalies\n", "anomalies = errors > threshold\n", "print(f\"Number of Anomalies Detected: {np.sum(anomalies)}\")\n", "\n", "# 9. Visualize Anomalies\n", "plt.figure(figsize=(12, 6))\n", "plt.plot(errors)\n", "plt.axhline(y=threshold, color='r', linestyle='--', label='Anomaly Threshold')\n", "plt.scatter(np.where(anomalies)[0], errors[anomalies], color='red', label='Anomalies')\n", "plt.title(\"Reconstruction Errors and Anomalies\")\n", "plt.xlabel(\"Time\")\n", "plt.ylabel(\"Reconstruction Error\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading data from folder: healthy\n", "Loading file: healthy/traindata_201911/traindata_20191107_080636.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191102_171055.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191115_080506.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191111_170435.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191123_080939.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191128_170246.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191126_080220.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191109_081204.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191123_171140.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191130_171042.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191118_080318.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191122_080649.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191112_170053.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191119_080043.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191128_080619.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191109_171114.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191105_170114.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191108_170517.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191110_171637.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191111_081134.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191120_081113.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191125_080555.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191115_170437.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191116_081423.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191114_080031.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191117_171711.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191104_170038.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191117_080019.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191112_080022.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191129_080556.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191101_171345.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191126_170240.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191121_080135.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191124_081359.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191127_080407.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191102_081127.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191122_170414.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191120_170142.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191104_080120.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191129_170000.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191116_171052.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191107_170000.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191108_080624.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191110_081640.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191124_171140.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191119_170235.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191118_170302.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191130_081116.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191103_171044.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191113_081216.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191127_170700.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191103_081124.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191125_170057.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191106_080626.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191114_170508.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191121_170024.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191101_081130.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191106_171005.xlsx\n", "Loading file: healthy/traindata_201911/traindata_20191113_170643.xlsx\n", "Loaded 59 files from healthy\n", "Loading data from folder: unhealthy\n", "Loading file: unhealthy/traindata_201903/traindata_20190313_080000.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190313_170024.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190329_081100.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190301_080625.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190317_081117.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190309_081103.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190319_170444.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190319_080437.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190306_080543.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190314_170138.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190315_080637.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190322_080118.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190330_081157.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190328_080627.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190326_170118.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190318_080648.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190324_171350.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190308_170257.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190312_170457.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190318_170503.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190324_081051.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190310_175139.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190329_170509.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190321_080617.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190326_080008.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190330_171250.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190316_171445.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190320_080014.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190320_170437.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190307_170641.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190310_081147.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190303_081229.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190323_081230.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190325_080606.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190307_080627.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190304_170014.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190325_170100.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190312_080535.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190305_080612.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190309_171118.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190327_080557.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190305_170006.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190321_170152.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190302_081036.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190314_081446.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190327_170446.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190316_081106.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190317_171150.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190331_161323.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190301_170009.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190302_170901.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190323_171140.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190304_080000.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190322_170236.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190311_170510.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190331_071129.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190311_080627.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190306_170441.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190303_171115.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190315_170152.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190328_170528.xlsx\n", "Loading file: unhealthy/traindata_201903/traindata_20190308_080554.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190412_070548.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190403_160454.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190426_070344.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190421_070058.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190415_160449.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190430_070031.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190421_161842.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190401_070257.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190428_071054.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190413_071424.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190405_070619.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190423_160047.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190424_074522.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190402_160000.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190430_160519.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190419_071442.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190412_160555.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190410_160209.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190419_160602.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190423_070637.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190414_071945.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190425_070037.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190404_070508.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190429_070016.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190403_070634.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190404_160104.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190407_072051.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190407_162149.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190418_070548.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190418_160124.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190422_070613.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190429_160100.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190410_070610.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190420_162202.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190411_160127.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190416_160431.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190417_160506.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190413_160826.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190426_160018.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190405_160231.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190414_161409.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190427_161103.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190416_070206.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190406_072353.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190425_160419.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190427_071113.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190424_160152.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190417_070628.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190420_070015.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190402_070711.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190422_162713.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190406_161354.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190408_070649.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190415_070600.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190428_161334.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190411_070110.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190401_160117.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190408_160526.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190409_070636.xlsx\n", "Loading file: unhealthy/traindata_201904/traindata_20190409_160554.xlsx\n", "Loaded 122 files from unhealthy\n", "Combined data shape: (8191579, 6)\n", "Starting data preprocessing...\n", "Missing values check after imputation: \n", "0    0\n", "1    0\n", "2    0\n", "3    0\n", "4    0\n", "5    0\n", "dtype: int64\n"]}, {"data": {"image/png": "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************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Data preprocessing completed.\n", "Starting feature extraction using Wavelet Transform...\n", "Shape of wavelet_features: (12, 6)\n", "Reshaping data for LSTM Autoencoder...\n", "Shape of Data Sequences: (0,)\n", "Error: Data sequences shape is not as expected!\n", "Building LSTM Autoencoder model...\n"]}, {"ename": "IndexError", "evalue": "tuple index out of range", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mIndexError\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 116\u001b[0m\n\u001b[1;32m    113\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m model\n\u001b[1;32m    115\u001b[0m \u001b[38;5;66;03m# LSTM Autoencoder Model\u001b[39;00m\n\u001b[0;32m--> 116\u001b[0m input_shape \u001b[38;5;241m=\u001b[39m (\u001b[43mdata_sequences\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mshape\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m]\u001b[49m, data_sequences\u001b[38;5;241m.\u001b[39mshape[\u001b[38;5;241m2\u001b[39m])\n\u001b[1;32m    117\u001b[0m autoencoder \u001b[38;5;241m=\u001b[39m build_lstm_autoencoder(input_shape)\n\u001b[1;32m    119\u001b[0m \u001b[38;5;66;03m# Train the autoencoder on healthy data\u001b[39;00m\n", "\u001b[0;31mIndexError\u001b[0m: tuple index out of range"]}], "source": ["import os\n", "import numpy as np\n", "import pandas as pd\n", "import pywt\n", "from sklearn.preprocessing import StandardScaler\n", "from tensorflow.keras.models import Sequential\n", "from tensorflow.keras.layers import LSTM, Dense, RepeatVector, TimeDistributed\n", "import matplotlib.pyplot as plt\n", "from openpyxl import load_workbook\n", "\n", "# 1. Load Data from Folder\n", "def load_data_from_folder(folder_path):\n", "    data = []\n", "    print(f\"Loading data from folder: {folder_path}\")\n", "    for subfolder in os.listdir(folder_path):\n", "        subfolder_path = os.path.join(folder_path, subfolder)\n", "        if os.path.isdir(subfolder_path):\n", "            for file in os.listdir(subfolder_path):\n", "                if file.endswith('.xlsx'):\n", "                    file_path = os.path.join(subfolder_path, file)\n", "                    print(f\"Loading file: {file_path}\")\n", "                    df = pd.read_excel(file_path, header=None)\n", "                    data.append(df.iloc[:, :6])  # Consider only the first 6 columns\n", "    print(f\"Loaded {len(data)} files from {folder_path}\")\n", "    return pd.concat(data, axis=0, ignore_index=True)\n", "\n", "healthy_folder = r'healthy'\n", "unhealthy_folder = r'unhealthy'\n", "\n", "healthy_data = load_data_from_folder(healthy_folder)\n", "unhealthy_data = load_data_from_folder(unhealthy_folder)\n", "\n", "# Combine healthy and unhealthy data for modeling\n", "data = pd.concat([healthy_data, unhealthy_data], axis=0, ignore_index=True)\n", "print(f\"Combined data shape: {data.shape}\")\n", "\n", "# 2. Data Preprocessing\n", "print(\"Starting data preprocessing...\")\n", "\n", "# Handle missing values (if any)\n", "data = data.fillna(method='ffill').fillna(method='bfill')  # Fill missing values with forward/backward fill\n", "\n", "# Verify that the data is complete after imputation\n", "print(f\"Missing values check after imputation: \\n{data.isnull().sum()}\")\n", "\n", "# Normalize the data (important for time series)\n", "scaler = StandardScaler()\n", "data_scaled = scaler.fit_transform(data)  # Normalize each feature across the time axis\n", "\n", "# Plot a sample to inspect the data\n", "plt.plot(data_scaled[:100, :])  # First 100 data points of the first sensor\n", "plt.title(\"Normalized Sensor Data - Sample\")\n", "plt.xlabel(\"Time\")\n", "plt.ylabel(\"Sensor Value\")\n", "plt.show()\n", "\n", "print(\"Data preprocessing completed.\")\n", "\n", "# 3. Dynamic Feature Extraction using Wavelet Transform\n", "print(\"Starting feature extraction using Wavelet Transform...\")\n", "\n", "def wavelet_transform(data, wavelet='db1', level=5):\n", "    features = []\n", "    for sensor_data in data.T:  # Iterate over each sensor (column)\n", "        coeffs = pywt.wavedec(sensor_data, wavelet, level=level)\n", "        # Extract approximation and detail coefficients at all levels\n", "        feature_vector = []\n", "        for coeff in coeffs:\n", "            feature_vector.append(np.mean(coeff))  # Mean of coefficients as a feature\n", "            feature_vector.append(np.std(coeff))  # Standard deviation as a feature\n", "        features.append(feature_vector)  # Each sensor gets its own feature vector\n", "    return np.array(features).T  # Ensure the shape is (samples, features)\n", "\n", "# Apply wavelet transform to the data (this should now give you many rows)\n", "wavelet_features = wavelet_transform(data_scaled)\n", "\n", "# Verify the new shape of wavelet_features\n", "print(f\"Shape of wavelet_features: {wavelet_features.shape}\")\n", "\n", "# 4. Reshape Data for LSTM Autoencoder\n", "print(\"Reshaping data for LSTM Autoencoder...\")\n", "\n", "def create_sequences(data, seq_length=50):\n", "    sequences = []\n", "    # Create sequences of length seq_length from the data\n", "    for i in range(len(data) - seq_length + 1):  # Ensure sequences are created correctly\n", "        sequences.append(data[i:i + seq_length])  # Data should have 3 dimensions\n", "    return np.array(sequences)\n", "\n", "# Prepare data for LSTM autoencoder\n", "seq_length = 50  # Sequence length should be fine since you have thousands of rows now\n", "data_sequences = create_sequences(wavelet_features, seq_length)\n", "\n", "# Check the shape of data_sequences after modification\n", "print(f\"Shape of Data Sequences: {data_sequences.shape}\")\n", "\n", "# Ensure data_sequences has 3 dimensions: (number_of_sequences, sequence_length, features)\n", "if len(data_sequences.shape) != 3:\n", "    print(\"Error: Data sequences shape is not as expected!\")\n", "else:\n", "    print(f\"Sequences created: {data_sequences.shape[0]} sequences, {data_sequences.shape[1]} time steps, {data_sequences.shape[2]} features\")\n", "\n", "# 5. Build LSTM Autoencoder Model\n", "print(\"Building LSTM Autoencoder model...\")\n", "\n", "def build_lstm_autoencoder(input_shape):\n", "    model = Sequential()\n", "    model.add(LSTM(128, activation='relu', input_shape=input_shape, return_sequences=False))\n", "    model.add(RepeatVector(input_shape[0]))\n", "    model.add(LSTM(128, activation='relu', return_sequences=True))\n", "    model.add(TimeDistributed(Den<PERSON>(input_shape[1])))\n", "    model.compile(optimizer='adam', loss='mse')\n", "    return model\n", "\n", "# LSTM Autoencoder Model\n", "input_shape = (data_sequences.shape[1], data_sequences.shape[2])\n", "autoencoder = build_lstm_autoencoder(input_shape)\n", "\n", "# Train the autoencoder on healthy data\n", "print(\"Training LSTM Autoencoder model...\")\n", "autoencoder.fit(data_sequences[:len(healthy_data)], data_sequences[:len(healthy_data)], epochs=50, batch_size=32, validation_split=0.2, verbose=1)\n", "\n", "print(\"Model training completed.\")\n", "\n", "# 6. Anomaly Detection: Calculate Reconstruction Errors\n", "print(\"Calculating reconstruction errors for anomaly detection...\")\n", "\n", "def calculate_reconstruction_errors(model, data):\n", "    predictions = model.predict(data)\n", "    errors = np.mean(np.abs(predictions - data), axis=(1, 2))  # Mean absolute error along time and feature axes\n", "    return errors\n", "\n", "# Calculate errors for the whole dataset\n", "errors = calculate_reconstruction_errors(autoencoder, data_sequences)\n", "print(f\"Calculated reconstruction errors for {len(errors)} data points.\")\n", "\n", "# 7. <PERSON> for Anomaly Detection\n", "print(\"Setting threshold for anomaly detection...\")\n", "\n", "threshold = np.percentile(errors, 95)  # Use 95th percentile of reconstruction error as threshold for anomaly\n", "print(f\"Anomaly detection threshold: {threshold}\")\n", "\n", "# 8. Detect Anomalies\n", "print(\"Detecting anomalies...\")\n", "anomalies = errors > threshold\n", "print(f\"Number of Anomalies Detected: {np.sum(anomalies)}\")\n", "\n", "# 9. Visualize Anomalies\n", "plt.figure(figsize=(12, 6))\n", "plt.plot(errors)\n", "plt.axhline(y=threshold, color='r', linestyle='--', label='Anomaly Threshold')\n", "plt.scatter(np.where(anomalies)[0], errors[anomalies], color='red', label='Anomalies')\n", "plt.title(\"Reconstruction Errors and Anomalies\")\n", "plt.xlabel(\"Time\")\n", "plt.ylabel(\"Reconstruction Error\")\n", "plt.legend()\n", "plt.show()\n", "\n", "print(\"Anomaly detection and visualization completed.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "dl-env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 2}