{"cells": [{"cell_type": "code", "execution_count": 4, "id": "8bfd0226", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Loading healthy data from /Users/<USER>/Documents/bridge_sensor/healthy...\n", "Loaded traindata_20191028_170815.xlsx: <PERSON><PERSON><PERSON> (69352, 6)\n", "Loaded traindata_20191031_080607.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20191029_080435.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191027_171117.xlsx: <PERSON><PERSON><PERSON> (38808, 6)\n", "Loaded traindata_20191026_071135.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20191026_161129.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20191030_170103.xlsx: <PERSON><PERSON><PERSON> (48714, 6)\n", "Loaded traindata_20191027_081146.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20191030_080606.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191029_170232.xlsx: <PERSON><PERSON><PERSON> (47889, 6)\n", "Loaded traindata_20191028_081019.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20191031_170250.xlsx: <PERSON><PERSON><PERSON> (47889, 6)\n", "Loaded traindata_20191025_160516.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20191107_080636.xlsx: <PERSON><PERSON><PERSON> (46238, 6)\n", "Loaded traindata_20191102_171055.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20191115_080506.xlsx: <PERSON><PERSON><PERSON> (78432, 6)\n", "Loaded traindata_20191111_170435.xlsx: <PERSON><PERSON><PERSON> (40459, 6)\n", "Loaded traindata_20191123_080939.xlsx: <PERSON><PERSON><PERSON> (40459, 6)\n", "Loaded traindata_20191128_170246.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20191126_080220.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20191109_081204.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20191123_171140.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20191130_171042.xlsx: <PERSON><PERSON><PERSON> (67701, 6)\n", "Loaded traindata_20191118_080318.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20191122_080649.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191112_170053.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20191119_080043.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191128_080619.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191109_171114.xlsx: <PERSON><PERSON><PERSON> (38808, 6)\n", "Loaded traindata_20191105_170114.xlsx: <PERSON><PERSON><PERSON> (39634, 6)\n", "Loaded traindata_20191108_170517.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20191110_171637.xlsx: <PERSON><PERSON><PERSON> (39634, 6)\n", "Loaded traindata_20191111_081134.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20191120_081113.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191125_080555.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191115_170437.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191116_081423.xlsx: <PERSON><PERSON><PERSON> (41285, 6)\n", "Loaded traindata_20191114_080031.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20191117_171711.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20191104_170038.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20191117_080019.xlsx: <PERSON><PERSON><PERSON> (45412, 6)\n", "Loaded traindata_20191112_080022.xlsx: <PERSON><PERSON><PERSON> (45412, 6)\n", "Loaded traindata_20191129_080556.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20191101_171345.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20191126_170240.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20191121_080135.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20191124_081359.xlsx: <PERSON><PERSON><PERSON> (39634, 6)\n", "Loaded traindata_20191127_080407.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191102_081127.xlsx: <PERSON><PERSON><PERSON> (51191, 6)\n", "Loaded traindata_20191122_170414.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20191120_170142.xlsx: <PERSON><PERSON><PERSON> (52016, 6)\n", "Loaded traindata_20191104_080120.xlsx: <PERSON><PERSON><PERSON> (47063, 6)\n", "Loaded traindata_20191129_170000.xlsx: <PERSON><PERSON><PERSON> (70966, 6)\n", "Loaded traindata_20191116_171052.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20191107_170000.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20191108_080624.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191110_081640.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20191124_171140.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20191119_170235.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20191118_170302.xlsx: <PERSON><PERSON><PERSON> (40459, 6)\n", "Loaded traindata_20191130_081116.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20191103_171044.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20191113_081216.xlsx: <PERSON><PERSON><PERSON> (57795, 6)\n", "Loaded traindata_20191127_170700.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20191103_081124.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20191125_170057.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20191106_080626.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20191114_170508.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20191121_170024.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191101_081130.xlsx: <PERSON><PERSON><PERSON> (38808, 6)\n", "Loaded traindata_20191106_171005.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20191113_170643.xlsx: <PERSON><PERSON><PERSON> (65224, 6)\n", "\n", "Loading unhealthy data from /Users/<USER>/Documents/bridge_sensor/unhealthy...\n", "Loaded traindata_20190313_080000.xlsx: <PERSON><PERSON><PERSON> (41248, 6)\n", "Loaded traindata_20190313_170024.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190329_081100.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190301_080625.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190317_081117.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20190309_081103.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190319_170444.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190319_080437.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190306_080543.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190314_170138.xlsx: <PERSON><PERSON><PERSON> (65224, 6)\n", "Loaded traindata_20190315_080637.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190322_080118.xlsx: <PERSON><PERSON><PERSON> (61922, 6)\n", "Loaded traindata_20190330_081157.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190328_080627.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190326_170118.xlsx: <PERSON><PERSON><PERSON> (41285, 6)\n", "Loaded traindata_20190318_080648.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190324_171350.xlsx: <PERSON><PERSON><PERSON> (38808, 6)\n", "Loaded traindata_20190308_170257.xlsx: <PERSON><PERSON><PERSON> (45412, 6)\n", "Loaded traindata_20190312_170457.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190318_170503.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190324_081051.xlsx: <PERSON><PERSON><PERSON> (38808, 6)\n", "Loaded traindata_20190310_175139.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190329_170509.xlsx: <PERSON><PERSON><PERSON> (52842, 6)\n", "Loaded traindata_20190321_080617.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190326_080008.xlsx: <PERSON><PERSON><PERSON> (47889, 6)\n", "Loaded traindata_20190330_171250.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190316_171445.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190320_080014.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20190320_170437.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190307_170641.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190310_081147.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190303_081229.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190323_081230.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190325_080606.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190307_080627.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190304_170014.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190325_170100.xlsx: <PERSON><PERSON><PERSON> (45412, 6)\n", "Loaded traindata_20190312_080535.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190305_080612.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190309_171118.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190327_080557.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20190305_170006.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190321_170152.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190302_081036.xlsx: <PERSON><PERSON><PERSON> (40459, 6)\n", "Loaded traindata_20190314_081446.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190327_170446.xlsx: <PERSON><PERSON><PERSON> (103197, 6)\n", "Loaded traindata_20190316_081106.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20190317_171150.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190331_161323.xlsx: <PERSON><PERSON><PERSON> (41285, 6)\n", "Loaded traindata_20190301_170009.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190302_170901.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190323_171140.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20190304_080000.xlsx: <PERSON><PERSON><PERSON> (42074, 6)\n", "Loaded traindata_20190322_170236.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190311_170510.xlsx: <PERSON><PERSON><PERSON> (58620, 6)\n", "Loaded traindata_20190331_071129.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190311_080627.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190306_170441.xlsx: <PERSON><PERSON><PERSON> (67701, 6)\n", "Loaded traindata_20190303_171115.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20190315_170152.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190328_170528.xlsx: <PERSON><PERSON><PERSON> (75956, 6)\n", "Loaded traindata_20190308_080554.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190412_070548.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190403_160454.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190426_070344.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190421_070058.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190415_160449.xlsx: <PERSON><PERSON><PERSON> (106499, 6)\n", "Loaded traindata_20190430_070031.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190421_161842.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190401_070257.xlsx: <PERSON><PERSON><PERSON> (58620, 6)\n", "Loaded traindata_20190428_071054.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190413_071424.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190405_070619.xlsx: <PERSON><PERSON><PERSON> (46238, 6)\n", "Loaded traindata_20190423_160047.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190424_074522.xlsx: <PERSON><PERSON><PERSON> (45412, 6)\n", "Loaded traindata_20190402_160000.xlsx: <PERSON><PERSON><PERSON> (45375, 6)\n", "Loaded traindata_20190430_160519.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190419_071442.xlsx: <PERSON><PERSON><PERSON> (41285, 6)\n", "Loaded traindata_20190412_160555.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190410_160209.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190419_160602.xlsx: <PERSON><PERSON><PERSON> (41285, 6)\n", "Loaded traindata_20190423_070637.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190414_071945.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190425_070037.xlsx: <PERSON><PERSON><PERSON> (40459, 6)\n", "Loaded traindata_20190404_070508.xlsx: <PERSON><PERSON><PERSON> (47063, 6)\n", "Loaded traindata_20190429_070016.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190403_070634.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190404_160104.xlsx: <PERSON><PERSON><PERSON> (45412, 6)\n", "Loaded traindata_20190407_072051.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20190407_162149.xlsx: <PERSON><PERSON><PERSON> (52016, 6)\n", "Loaded traindata_20190418_070548.xlsx: <PERSON><PERSON><PERSON> (81734, 6)\n", "Loaded traindata_20190418_160124.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190422_070613.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20190429_160100.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190410_070610.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190420_162202.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190411_160127.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190416_160431.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190417_160506.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20190413_160826.xlsx: <PERSON><PERSON><PERSON> (40459, 6)\n", "Loaded traindata_20190426_160018.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20190405_160231.xlsx: <PERSON><PERSON><PERSON> (51191, 6)\n", "Loaded traindata_20190414_161409.xlsx: <PERSON><PERSON><PERSON> (41285, 6)\n", "Loaded traindata_20190427_161103.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190416_070206.xlsx: <PERSON><PERSON><PERSON> (39634, 6)\n", "Loaded traindata_20190406_072353.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190425_160419.xlsx: <PERSON><PERSON><PERSON> (48714, 6)\n", "Loaded traindata_20190427_071113.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20190424_160152.xlsx: <PERSON><PERSON><PERSON> (56144, 6)\n", "Loaded traindata_20190417_070628.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190420_070015.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190402_070711.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190422_162713.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190406_161354.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190408_070649.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20190415_070600.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190428_161334.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190411_070110.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190401_160117.xlsx: <PERSON><PERSON><PERSON> (46238, 6)\n", "Loaded traindata_20190408_160526.xlsx: <PERSON><PERSON><PERSON> (106499, 6)\n", "Loaded traindata_20190409_070636.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190409_160554.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "\n", "Plotting acceleration-time graphs...\n", "\n", "Extracting features...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_50413/**********.py:72: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_50413/**********.py:75: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_50413/**********.py:72: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_50413/**********.py:75: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_50413/**********.py:72: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_50413/**********.py:75: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_50413/**********.py:72: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_50413/**********.py:75: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_50413/**********.py:72: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_50413/**********.py:75: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_50413/**********.py:72: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_50413/**********.py:75: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_50413/**********.py:72: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_50413/**********.py:75: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_50413/**********.py:72: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_50413/**********.py:75: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_50413/**********.py:72: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_50413/**********.py:75: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_50413/**********.py:72: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_50413/**********.py:75: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Feature matrix shape: (7760, 78)\n", "Labels shape: (7760,)\n", "\n", "Computing feature statistics...\n", "NaN values in feature matrix: 3600\n", "Infinite values in feature matrix: 0\n", "Class distribution: Healthy (1): 2880, Unhealthy (0): 4880\n", "Feature statistics saved to img/feature_stats.txt\n", "\n", "Performing RandomizedSearchCV with XGBoost...\n"]}, {"ename": "AttributeError", "evalue": "'super' object has no attribute '__sklearn_tags__'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[4], line 406\u001b[0m\n\u001b[1;32m    403\u001b[0m     plt\u001b[38;5;241m.\u001b[39mclose()\n\u001b[1;32m    405\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;18m__name__\u001b[39m \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m__main__\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m--> 406\u001b[0m     \u001b[43mmain\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[4], line 342\u001b[0m, in \u001b[0;36mmain\u001b[0;34m()\u001b[0m\n\u001b[1;32m    321\u001b[0m param_dist \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m    322\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmax_depth\u001b[39m\u001b[38;5;124m'\u001b[39m: [\u001b[38;5;241m3\u001b[39m, \u001b[38;5;241m5\u001b[39m, \u001b[38;5;241m7\u001b[39m, \u001b[38;5;241m9\u001b[39m],\n\u001b[1;32m    323\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mlearning_rate\u001b[39m\u001b[38;5;124m'\u001b[39m: [\u001b[38;5;241m0.05\u001b[39m, \u001b[38;5;241m0.1\u001b[39m, \u001b[38;5;241m0.15\u001b[39m, \u001b[38;5;241m0.2\u001b[39m, \u001b[38;5;241m0.3\u001b[39m],\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    328\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmin_child_weight\u001b[39m\u001b[38;5;124m'\u001b[39m: [\u001b[38;5;241m1\u001b[39m, \u001b[38;5;241m3\u001b[39m, \u001b[38;5;241m5\u001b[39m]\n\u001b[1;32m    329\u001b[0m }\n\u001b[1;32m    331\u001b[0m random_search \u001b[38;5;241m=\u001b[39m RandomizedSearchCV(\n\u001b[1;32m    332\u001b[0m     estimator\u001b[38;5;241m=\u001b[39mxgb,\n\u001b[1;32m    333\u001b[0m     param_distributions\u001b[38;5;241m=\u001b[39mparam_dist,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    339\u001b[0m     random_state\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m42\u001b[39m\n\u001b[1;32m    340\u001b[0m )\n\u001b[0;32m--> 342\u001b[0m \u001b[43mrandom_search\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfit\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX_train_scaled\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my_train\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    344\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124mRandomizedSearchCV Results:\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    345\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mBest parameters:\u001b[39m\u001b[38;5;124m\"\u001b[39m, random_search\u001b[38;5;241m.\u001b[39mbest_params_)\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/sklearn/base.py:1389\u001b[0m, in \u001b[0;36m_fit_context.<locals>.decorator.<locals>.wrapper\u001b[0;34m(estimator, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1382\u001b[0m     estimator\u001b[38;5;241m.\u001b[39m_validate_params()\n\u001b[1;32m   1384\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m config_context(\n\u001b[1;32m   1385\u001b[0m     skip_parameter_validation\u001b[38;5;241m=\u001b[39m(\n\u001b[1;32m   1386\u001b[0m         prefer_skip_nested_validation \u001b[38;5;129;01mor\u001b[39;00m global_skip_validation\n\u001b[1;32m   1387\u001b[0m     )\n\u001b[1;32m   1388\u001b[0m ):\n\u001b[0;32m-> 1389\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfit_method\u001b[49m\u001b[43m(\u001b[49m\u001b[43mestimator\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/sklearn/model_selection/_search.py:933\u001b[0m, in \u001b[0;36mBaseSearchCV.fit\u001b[0;34m(self, X, y, **params)\u001b[0m\n\u001b[1;32m    929\u001b[0m params \u001b[38;5;241m=\u001b[39m _check_method_params(X, params\u001b[38;5;241m=\u001b[39mparams)\n\u001b[1;32m    931\u001b[0m routed_params \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_routed_params_for_fit(params)\n\u001b[0;32m--> 933\u001b[0m cv_orig \u001b[38;5;241m=\u001b[39m check_cv(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcv, y, classifier\u001b[38;5;241m=\u001b[39m\u001b[43mis_classifier\u001b[49m\u001b[43m(\u001b[49m\u001b[43mestimator\u001b[49m\u001b[43m)\u001b[49m)\n\u001b[1;32m    934\u001b[0m n_splits \u001b[38;5;241m=\u001b[39m cv_orig\u001b[38;5;241m.\u001b[39mget_n_splits(X, y, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mrouted_params\u001b[38;5;241m.\u001b[39msplitter\u001b[38;5;241m.\u001b[39msplit)\n\u001b[1;32m    936\u001b[0m base_estimator \u001b[38;5;241m=\u001b[39m clone(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mestimator)\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/sklearn/base.py:1237\u001b[0m, in \u001b[0;36mis_classifier\u001b[0;34m(estimator)\u001b[0m\n\u001b[1;32m   1230\u001b[0m     warnings\u001b[38;5;241m.\u001b[39mwarn(\n\u001b[1;32m   1231\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpassing a class to \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mprint\u001b[39m(inspect\u001b[38;5;241m.\u001b[39mstack()[\u001b[38;5;241m0\u001b[39m][\u001b[38;5;241m3\u001b[39m])\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m is deprecated and \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1232\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mwill be removed in 1.8. Use an instance of the class instead.\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m   1233\u001b[0m         \u001b[38;5;167;01mFutureWarning\u001b[39;00m,\n\u001b[1;32m   1234\u001b[0m     )\n\u001b[1;32m   1235\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mgetattr\u001b[39m(estimator, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_estimator_type\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m) \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mclassifier\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m-> 1237\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mget_tags\u001b[49m\u001b[43m(\u001b[49m\u001b[43mestimator\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39mestimator_type \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mclassifier\u001b[39m\u001b[38;5;124m\"\u001b[39m\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/sklearn/utils/_tags.py:430\u001b[0m, in \u001b[0;36mget_tags\u001b[0;34m(estimator)\u001b[0m\n\u001b[1;32m    428\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m klass \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mreversed\u001b[39m(\u001b[38;5;28mtype\u001b[39m(estimator)\u001b[38;5;241m.\u001b[39mmro()):\n\u001b[1;32m    429\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m__sklearn_tags__\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mvars\u001b[39m(klass):\n\u001b[0;32m--> 430\u001b[0m         sklearn_tags_provider[klass] \u001b[38;5;241m=\u001b[39m \u001b[43mklass\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m__sklearn_tags__\u001b[49m\u001b[43m(\u001b[49m\u001b[43mestimator\u001b[49m\u001b[43m)\u001b[49m  \u001b[38;5;66;03m# type: ignore[attr-defined]\u001b[39;00m\n\u001b[1;32m    431\u001b[0m         class_order\u001b[38;5;241m.\u001b[39mappend(klass)\n\u001b[1;32m    432\u001b[0m     \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_more_tags\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mvars\u001b[39m(klass):\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/sklearn/base.py:540\u001b[0m, in \u001b[0;36mClassifierMixin.__sklearn_tags__\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    539\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__sklearn_tags__\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[0;32m--> 540\u001b[0m     tags \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m__sklearn_tags__\u001b[49m()\n\u001b[1;32m    541\u001b[0m     tags\u001b[38;5;241m.\u001b[39mestimator_type \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mclassifier\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    542\u001b[0m     tags\u001b[38;5;241m.\u001b[39mclassifier_tags \u001b[38;5;241m=\u001b[39m ClassifierTags()\n", "\u001b[0;31mAttributeError\u001b[0m: 'super' object has no attribute '__sklearn_tags__'"]}], "source": ["import os\n", "import pandas as pd\n", "import numpy as np\n", "from scipy.fft import fft\n", "from scipy.stats import kurtosis, skew, entropy\n", "from sklearn.model_selection import train_test_split, RandomizedSearchCV\n", "from sklearn.preprocessing import StandardScaler\n", "from xgboost import XGBClassifier\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc\n", "\n", "# Set random seed for reproducibility\n", "np.random.seed(42)\n", "\n", "# 1. Data Loading Function\n", "def load_sensor_data(base_path, condition):\n", "    data_list = []\n", "    folder_path = os.path.join(base_path, condition)\n", "    print(f\"\\nLoading {condition} data from {folder_path}...\")\n", "    \n", "    if not os.path.exists(folder_path):\n", "        print(f\"Error: Directory {folder_path} does not exist.\")\n", "        return data_list\n", "    \n", "    for root, _, files in os.walk(folder_path):\n", "        for file in files:\n", "            if file.endswith('.xlsx'):\n", "                file_path = os.path.join(root, file)\n", "                try:\n", "                    df = pd.read_excel(file_path, usecols=range(6))\n", "                    print(f\"Loaded {file}: Shape {df.shape}\")\n", "                    data_list.append(df)\n", "                except Exception as e:\n", "                    print(f\"Error loading {file}: {str(e)}\")\n", "    \n", "    return data_list\n", "\n", "# 2. Advanced Feature Extraction Function\n", "def extract_features(df, sampling_freq=1651, start_sec=2, duration_sec=20, window_sec=0.5):\n", "    start_idx = start_sec * sampling_freq\n", "    end_idx = start_idx + (duration_sec * sampling_freq)\n", "    \n", "    if start_idx >= len(df):\n", "        print(f\"Warning: Start index exceeds data length for file with {len(df)} samples.\")\n", "        return pd.DataFrame()\n", "    if end_idx > len(df):\n", "        print(f\"Warning: Data truncated from {len(df)} to {end_idx} samples.\")\n", "        end_idx = len(df)\n", "    \n", "    df_chunk = df.iloc[start_idx:end_idx]\n", "    n_samples = len(df_chunk)\n", "    window_size = int(window_sec * sampling_freq)\n", "    n_windows = n_samples // window_size\n", "    \n", "    if n_windows == 0:\n", "        print(f\"Warning: No complete windows available in chunk of {n_samples} samples.\")\n", "        return pd.DataFrame()\n", "    \n", "    features_list = []\n", "    for sensor in range(6):\n", "        sensor_data = df_chunk.iloc[:, sensor]\n", "        sensor_features = []\n", "        \n", "        for i in range(n_windows):\n", "            chunk = sensor_data[i*window_size:(i+1)*window_size]\n", "            \n", "            # Time-domain features\n", "            rms = np.sqrt(np.mean(chunk**2))\n", "            peak_to_peak = np.max(chunk) - np.min(chunk)\n", "            zero_crossings = np.sum(np.diff(np.sign(chunk)) != 0) / 2\n", "            kurt = kurtosis(chunk, nan_policy='omit')\n", "            crest_factor = np.max(np.abs(chunk)) / rms if rms != 0 else 0\n", "            mad = np.mean(np.abs(chunk - np.mean(chunk)))\n", "            skw = skew(chunk, nan_policy='omit')\n", "            energy = np.sum(chunk**2)\n", "            \n", "            # Frequency-domain features\n", "            fft_vals = fft(chunk.to_numpy())\n", "            fft_magnitude = np.abs(fft_vals)[:window_size//2]\n", "            freqs = np.fft.fftfreq(window_size, 1/sampling_freq)[:window_size//2]\n", "            dominant_freq = freqs[np.argmax(fft_magnitude)] if fft_magnitude.size > 0 else 0\n", "            spectral_power = np.sum(fft_magnitude**2) / window_size if fft_magnitude.size > 0 else 0\n", "            fft_magnitude_sorted = np.sort(fft_magnitude)[::-1]\n", "            second_dominant_freq = freqs[np.argsort(fft_magnitude)[-2]] if len(freqs) > 1 else 0\n", "            spectral_entropy = entropy(fft_magnitude / np.sum(fft_magnitude)) if np.sum(fft_magnitude) != 0 else 0\n", "            spectral_centroid = np.sum(freqs * fft_magnitude) / np.sum(fft_magnitude) if np.sum(fft_magnitude) != 0 else 0\n", "            \n", "            feat = {\n", "                f'sensor_{sensor}_rms': rms,\n", "                f'sensor_{sensor}_peak_to_peak': peak_to_peak,\n", "                f'sensor_{sensor}_zero_crossings': zero_crossings,\n", "                f'sensor_{sensor}_kurtosis': kurt,\n", "                f'sensor_{sensor}_crest_factor': crest_factor,\n", "                f'sensor_{sensor}_mad': mad,\n", "                f'sensor_{sensor}_skewness': skw,\n", "                f'sensor_{sensor}_energy': energy,\n", "                f'sensor_{sensor}_dominant_freq': dominant_freq,\n", "                f'sensor_{sensor}_second_dominant_freq': second_dominant_freq,\n", "                f'sensor_{sensor}_spectral_power': spectral_power,\n", "                f'sensor_{sensor}_spectral_entropy': spectral_entropy,\n", "                f'sensor_{sensor}_spectral_centroid': spectral_centroid\n", "            }\n", "            sensor_features.append(feat)\n", "        \n", "        features_list.append(pd.DataFrame(sensor_features))\n", "    \n", "    return pd.concat(features_list, axis=1)\n", "\n", "# 3. Feature Statistics Function\n", "def compute_feature_statistics(X, y, save_path=\"img/feature_stats.txt\"):\n", "    \"\"\"\n", "    Compute and save summary statistics for the feature matrix X, grouped by class.\n", "    \"\"\"\n", "    print(\"\\nComputing feature statistics...\")\n", "    \n", "    # Check for missing or invalid values\n", "    nan_count = X.isna().sum().sum()\n", "    inf_count = np.isinf(X).sum().sum()\n", "    print(f\"NaN values in feature matrix: {nan_count}\")\n", "    print(f\"Infinite values in feature matrix: {inf_count}\")\n", "    \n", "    # Replace NaNs with median and infinities with max/min\n", "    X_clean = <PERSON>.fillna(X.median()).replace([np.inf, -np.inf], [X.max(), X.min()])\n", "    \n", "    # Class distribution\n", "    healthy_count = sum(y == 1)\n", "    unhealthy_count = sum(y == 0)\n", "    print(f\"Class distribution: Healthy (1): {healthy_count}, Unhealthy (0): {unhealthy_count}\")\n", "    \n", "    # Summary statistics\n", "    stats = X_clean.describe().T[['mean', 'std', 'min', 'max']]\n", "    stats['variance'] = stats['std'] ** 2\n", "    \n", "    # Group by class\n", "    healthy_stats = X_clean[y == 1].describe().T[['mean', 'std']]\n", "    unhealthy_stats = X_clean[y == 0].describe().T[['mean', 'std']]\n", "    stats = stats.join(healthy_stats.rename(columns={'mean': 'healthy_mean', 'std': 'healthy_std'}))\n", "    stats = stats.join(unhealthy_stats.rename(columns={'mean': 'unhealthy_mean', 'std': 'unhealthy_std'}))\n", "    \n", "    # Save to file\n", "    with open(save_path, 'w') as f:\n", "        f.write(\"Feature Statistics\\n\")\n", "        f.write(f\"NaN values: {nan_count}\\n\")\n", "        f.write(f\"Infinite values: {inf_count}\\n\")\n", "        f.write(f\"Class distribution: Healthy: {healthy_count}, Unhealthy: {unhealthy_count}\\n\\n\")\n", "        f.write(stats.to_string())\n", "    \n", "    print(f\"Feature statistics saved to {save_path}\")\n", "    return stats\n", "\n", "# 4. Visualization Functions\n", "def plot_acceleration_time(healthy_data, unhealthy_data, sampling_freq=1651, n_samples=3):\n", "    \"\"\"\n", "    Plot and save acceleration vs. time for healthy and unhealthy samples.\n", "    \"\"\"\n", "    healthy_sample = healthy_data[:n_samples]\n", "    unhealthy_sample = unhealthy_data[:n_samples]\n", "    max_samples = int(25 * sampling_freq)\n", "    \n", "    for i, df in enumerate(healthy_sample):\n", "        n_samples = min(len(df), max_samples)\n", "        time = np.arange(0, n_samples/sampling_freq, 1/sampling_freq)\n", "        plt.figure(figsize=(3.5, 3))\n", "        df_plot = df.iloc[:n_samples]\n", "        for sensor in range(6):\n", "            plt.plot(time, df_plot.iloc[:, sensor], label=f'Sensor {sensor}', alpha=0.7)\n", "        plt.title(f'<PERSON><PERSON> Sample {i+1}', fontsize=10)\n", "        plt.xlabel('Time (s)', fontsize=8)\n", "        plt.ylabel('Acceleration', fontsize=8)\n", "        plt.legend(fontsize=7)\n", "        plt.grid(True)\n", "        plt.tick_params(axis='both', labelsize=7)\n", "        plt.tight_layout()\n", "        plt.savefig(f\"img/healthy_time_{i+1}.png\", dpi=300, bbox_inches='tight')\n", "        plt.close()\n", "    \n", "    for i, df in enumerate(unhealthy_sample):\n", "        n_samples = min(len(df), max_samples)\n", "        time = np.arange(0, n_samples/sampling_freq, 1/sampling_freq)\n", "        plt.figure(figsize=(3.5, 3))\n", "        df_plot = df.iloc[:n_samples]\n", "        for sensor in range(6):\n", "            plt.plot(time, df_plot.iloc[:, sensor], label=f'Sensor {sensor}', alpha=0.7)\n", "        plt.title(f'Unhealthy Sample {i+1}', fontsize=10)\n", "        plt.xlabel('Time (s)', fontsize=8)\n", "        plt.ylabel('Acceleration', fontsize=8)\n", "        plt.legend(fontsize=7)\n", "        plt.grid(True)\n", "        plt.tick_params(axis='both', labelsize=7)\n", "        plt.tight_layout()\n", "        plt.savefig(f\"img/unhealthy_time_{i+1}.png\", dpi=300, bbox_inches='tight')\n", "        plt.close()\n", "\n", "def plot_feature_distribution(X, y):\n", "    \"\"\"\n", "    Plot violin plot of selected features for healthy and unhealthy classes.\n", "    \"\"\"\n", "    features_df = pd.DataFrame({\n", "        \"RMS\": X[\"sensor_0_rms\"],\n", "        \"Kurtosis\": X[\"sensor_0_kurtosis\"],\n", "        \"Spectral_Entropy\": X[\"sensor_0_spectral_entropy\"],\n", "        \"Class\": [\"Healthy\" if label == 1 else \"Unhealthy\" for label in y]\n", "    })\n", "    \n", "    plt.figure(figsize=(3.5, 3))\n", "    sns.violinplot(data=features_df.melt(id_vars=\"Class\"), x=\"variable\", y=\"value\", hue=\"Class\", split=True)\n", "    plt.xlabel(\"Feature\", fontsize=8)\n", "    plt.ylabel(\"Feature Value\", fontsize=8)\n", "    plt.title(\"Feature Distributions\", fontsize=10)\n", "    plt.tick_params(axis='both', labelsize=7)\n", "    plt.legend(title=\"Class\", fontsize=7)\n", "    plt.tight_layout()\n", "    plt.savefig(\"img/feature_distribution.png\", dpi=300, bbox_inches='tight')\n", "    plt.close()\n", "\n", "def plot_feature_trends(healthy_data, unhealthy_data):\n", "    \"\"\"\n", "    Plot RMS and spectral power over windows for one healthy and one unhealthy file.\n", "    \"\"\"\n", "    if not healthy_data or not unhealthy_data:\n", "        print(\"Warning: No data available for feature trends plot.\")\n", "        return\n", "    \n", "    healthy_features = extract_features(healthy_data[0])\n", "    unhealthy_features = extract_features(unhealthy_data[0])\n", "    \n", "    if healthy_features.empty or unhealthy_features.empty:\n", "        print(\"Warning: Feature extraction failed for trends plot.\")\n", "        return\n", "    \n", "    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(3.5, 3), sharex=True)\n", "    windows = range(len(healthy_features))\n", "    ax1.plot(windows, healthy_features[\"sensor_0_rms\"], label=\"Healthy\")\n", "    ax1.plot(windows, unhealthy_features[\"sensor_0_rms\"], label=\"Unhealthy\")\n", "    ax2.plot(windows, healthy_features[\"sensor_0_spectral_power\"], label=\"Healthy\")\n", "    ax2.plot(windows, unhealthy_features[\"sensor_0_spectral_power\"], label=\"Unhealthy\")\n", "    ax1.set_title(\"RMS Over Windows\", fontsize=10)\n", "    ax2.set_title(\"Spectral Power Over Windows\", fontsize=10)\n", "    ax1.set_ylabel(\"RMS\", fontsize=8)\n", "    ax2.set_ylabel(\"Spectral Power\", fontsize=8)\n", "    ax2.set_xlabel(\"Window Index\", fontsize=8)\n", "    ax1.tick_params(axis='both', labelsize=7)\n", "    ax2.tick_params(axis='both', labelsize=7)\n", "    ax1.legend(fontsize=7)\n", "    ax2.legend(fontsize=7)\n", "    plt.tight_layout()\n", "    plt.savefig(\"img/feature_trends.png\", dpi=300, bbox_inches='tight')\n", "    plt.close()\n", "\n", "# 5. Main Processing Pipeline\n", "def main():\n", "    base_path = \"/Users/<USER>/Documents/bridge_sensor/\"\n", "    os.makedirs(\"img\", exist_ok=True)\n", "    \n", "    # Load data\n", "    healthy_data = load_sensor_data(base_path, \"healthy\")\n", "    unhealthy_data = load_sensor_data(base_path, \"unhealthy\")\n", "    \n", "    if not healthy_data and not unhealthy_data:\n", "        print(\"Error: No data loaded from either healthy or unhealthy folders.\")\n", "        return\n", "    \n", "    # Visualize time-series\n", "    print(\"\\nPlotting acceleration-time graphs...\")\n", "    plot_acceleration_time(healthy_data, unhealthy_data, n_samples=3)\n", "    \n", "    # Extract features\n", "    print(\"\\nExtracting features...\")\n", "    X_healthy = []\n", "    y_healthy = []\n", "    for df in healthy_data:\n", "        features = extract_features(df)\n", "        if not features.empty:\n", "            X_healthy.append(features)\n", "            y_healthy.extend([1] * len(features))\n", "    \n", "    X_unhealthy = []\n", "    y_unhealthy = []\n", "    for df in unhealthy_data:\n", "        features = extract_features(df)\n", "        if not features.empty:\n", "            X_unhealthy.append(features)\n", "            y_unhealthy.extend([0] * len(features))\n", "    \n", "    if not X_healthy and not X_unhealthy:\n", "        print(\"Error: No valid data extracted from any files.\")\n", "        return\n", "    \n", "    X = pd.concat(X_healthy + X_unhealthy, axis=0)\n", "    y = np.array(y_healthy + y_unhealthy)\n", "    \n", "    print(f\"\\nFeature matrix shape: {X.shape}\")\n", "    print(f\"Labels shape: {y.shape}\")\n", "    \n", "    # Compute feature statistics\n", "    stats = compute_feature_statistics(X, y)\n", "    \n", "    # Plot feature distribution and trends\n", "    plot_feature_distribution(X, y)\n", "    plot_feature_trends(healthy_data, unhealthy_data)\n", "    \n", "    # Split data\n", "    X_train, X_test, y_train, y_test = train_test_split(\n", "        X, y, test_size=0.2, random_state=42, stratify=y\n", "    )\n", "    \n", "    # Scale features\n", "    scaler = StandardScaler()\n", "    X_train_scaled = scaler.fit_transform(X_train)\n", "    X_test_scaled = scaler.transform(X_test)\n", "    \n", "    # XGBoost with RandomizedSearchCV\n", "    print(\"\\nPerforming RandomizedSearchCV with XGBoost...\")\n", "    xgb = XGBClassifier(\n", "        random_state=42,\n", "        eval_metric='logloss',\n", "        scale_pos_weight=sum(y==0)/sum(y==1)\n", "    )\n", "    \n", "    param_dist = {\n", "        'max_depth': [3, 5, 7, 9],\n", "        'learning_rate': [0.05, 0.1, 0.15, 0.2, 0.3],\n", "        'n_estimators': [100, 200, 300, 400, 500],\n", "        'subsample': [0.6, 0.7, 0.8, 0.9, 1.0],\n", "        'colsample_bytree': [0.6, 0.7, 0.8, 0.9, 1.0],\n", "        'gamma': [0, 0.1, 0.2, 0.3],\n", "        'min_child_weight': [1, 3, 5]\n", "    }\n", "    \n", "    random_search = RandomizedSearchCV(\n", "        estimator=xgb,\n", "        param_distributions=param_dist,\n", "        n_iter=50,\n", "        cv=5,\n", "        scoring='accuracy',\n", "        n_jobs=-1,\n", "        verbose=1,\n", "        random_state=42\n", "    )\n", "    \n", "    random_search.fit(X_train_scaled, y_train)\n", "    \n", "    print(\"\\nRandomizedSearchCV Results:\")\n", "    print(\"Best parameters:\", random_search.best_params_)\n", "    print(\"Best cross-validation score:\", random_search.best_score_)\n", "    \n", "    # Evaluate the best model\n", "    best_model = random_search.best_estimator_\n", "    y_pred = best_model.predict(X_test_scaled)\n", "    \n", "    print(\"\\nClassification Report:\")\n", "    print(classification_report(y_test, y_pred, target_names=['Unhealthy (0)', 'Healthy (1)']))\n", "    \n", "    # Plot confusion matrix\n", "    plt.figure(figsize=(3.5, 3))\n", "    cm = confusion_matrix(y_test, y_pred)\n", "    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \n", "                xticklabels=['Unhealthy', 'Healthy'], \n", "                yticklabels=['Unhealthy', 'Healthy'])\n", "    plt.title('Confusion Matrix', fontsize=10)\n", "    plt.ylabel('True Label', fontsize=8)\n", "    plt.xlabel('Predicted Label', fontsize=8)\n", "    plt.tick_params(axis='both', labelsize=7)\n", "    plt.tight_layout()\n", "    plt.savefig(\"img/confusion_matrix.png\", dpi=300, bbox_inches='tight')\n", "    plt.close()\n", "    \n", "    # Plot ROC curve\n", "    y_pred_proba = best_model.predict_proba(X_test_scaled)[:, 1]\n", "    fpr, tpr, _ = roc_curve(y_test, y_pred_proba)\n", "    roc_auc = auc(fpr, tpr)\n", "    \n", "    plt.figure(figsize=(3.5, 3))\n", "    plt.plot(fpr, tpr, color='darkorange', lw=1, \n", "             label=f'ROC curve (AUC = {roc_auc:.2f})')\n", "    plt.plot([0, 1], [0, 1], color='navy', lw=1, linestyle='--')\n", "    plt.xlim([0.0, 1.0])\n", "    plt.ylim([0.0, 1.05])\n", "    plt.xlabel('False Positive Rate', fontsize=8)\n", "    plt.ylabel('True Positive Rate', fontsize=8)\n", "    plt.title('ROC Curve', fontsize=10)\n", "    plt.legend(loc=\"lower right\", fontsize=7)\n", "    plt.tick_params(axis='both', labelsize=7)\n", "    plt.tight_layout()\n", "    plt.savefig(\"img/roc_curve.png\", dpi=300, bbox_inches='tight')\n", "    plt.close()\n", "    \n", "    # Plot feature importance\n", "    plt.figure(figsize=(3.5, 3))\n", "    feat_importance = pd.DataFrame({\n", "        'feature': X.columns,\n", "        'importance': best_model.feature_importances_\n", "    })\n", "    feat_importance = feat_importance.sort_values('importance', ascending=False)\n", "    sns.barplot(x='importance', y='feature', data=feat_importance.head(10))\n", "    plt.title('Top 10 Feature Importances', fontsize=10)\n", "    plt.xlabel('Importance Score', fontsize=8)\n", "    plt.ylabel('Feature', fontsize=8)\n", "    plt.tick_params(axis='both', labelsize=7)\n", "    plt.tight_layout()\n", "    plt.savefig(\"img/feature_importance.png\", dpi=300, bbox_inches='tight')\n", "    plt.close()\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": 1, "id": "6d0fb309", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["scikit-learn version: 1.6.1\n", "xgboost version: 3.0.0\n", "\n", "Loading healthy data from /Users/<USER>/Documents/bridge_sensor/healthy...\n", "Loaded traindata_20191028_170815.xlsx: <PERSON><PERSON><PERSON> (69352, 6)\n", "Loaded traindata_20191031_080607.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20191029_080435.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191027_171117.xlsx: <PERSON><PERSON><PERSON> (38808, 6)\n", "Loaded traindata_20191026_071135.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20191026_161129.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20191030_170103.xlsx: <PERSON><PERSON><PERSON> (48714, 6)\n", "Loaded traindata_20191027_081146.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20191030_080606.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191029_170232.xlsx: <PERSON><PERSON><PERSON> (47889, 6)\n", "Loaded traindata_20191028_081019.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20191031_170250.xlsx: <PERSON><PERSON><PERSON> (47889, 6)\n", "Loaded traindata_20191025_160516.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20191107_080636.xlsx: <PERSON><PERSON><PERSON> (46238, 6)\n", "Loaded traindata_20191102_171055.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20191115_080506.xlsx: <PERSON><PERSON><PERSON> (78432, 6)\n", "Loaded traindata_20191111_170435.xlsx: <PERSON><PERSON><PERSON> (40459, 6)\n", "Loaded traindata_20191123_080939.xlsx: <PERSON><PERSON><PERSON> (40459, 6)\n", "Loaded traindata_20191128_170246.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20191126_080220.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20191109_081204.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20191123_171140.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20191130_171042.xlsx: <PERSON><PERSON><PERSON> (67701, 6)\n", "Loaded traindata_20191118_080318.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20191122_080649.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191112_170053.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20191119_080043.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191128_080619.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191109_171114.xlsx: <PERSON><PERSON><PERSON> (38808, 6)\n", "Loaded traindata_20191105_170114.xlsx: <PERSON><PERSON><PERSON> (39634, 6)\n", "Loaded traindata_20191108_170517.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20191110_171637.xlsx: <PERSON><PERSON><PERSON> (39634, 6)\n", "Loaded traindata_20191111_081134.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20191120_081113.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191125_080555.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191115_170437.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191116_081423.xlsx: <PERSON><PERSON><PERSON> (41285, 6)\n", "Loaded traindata_20191114_080031.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20191117_171711.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20191104_170038.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20191117_080019.xlsx: <PERSON><PERSON><PERSON> (45412, 6)\n", "Loaded traindata_20191112_080022.xlsx: <PERSON><PERSON><PERSON> (45412, 6)\n", "Loaded traindata_20191129_080556.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20191101_171345.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20191126_170240.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20191121_080135.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20191124_081359.xlsx: <PERSON><PERSON><PERSON> (39634, 6)\n", "Loaded traindata_20191127_080407.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191102_081127.xlsx: <PERSON><PERSON><PERSON> (51191, 6)\n", "Loaded traindata_20191122_170414.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20191120_170142.xlsx: <PERSON><PERSON><PERSON> (52016, 6)\n", "Loaded traindata_20191104_080120.xlsx: <PERSON><PERSON><PERSON> (47063, 6)\n", "Loaded traindata_20191129_170000.xlsx: <PERSON><PERSON><PERSON> (70966, 6)\n", "Loaded traindata_20191116_171052.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20191107_170000.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20191108_080624.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191110_081640.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20191124_171140.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20191119_170235.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20191118_170302.xlsx: <PERSON><PERSON><PERSON> (40459, 6)\n", "Loaded traindata_20191130_081116.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20191103_171044.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20191113_081216.xlsx: <PERSON><PERSON><PERSON> (57795, 6)\n", "Loaded traindata_20191127_170700.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20191103_081124.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20191125_170057.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20191106_080626.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20191114_170508.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20191121_170024.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191101_081130.xlsx: <PERSON><PERSON><PERSON> (38808, 6)\n", "Loaded traindata_20191106_171005.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20191113_170643.xlsx: <PERSON><PERSON><PERSON> (65224, 6)\n", "\n", "Loading unhealthy data from /Users/<USER>/Documents/bridge_sensor/unhealthy...\n", "Loaded traindata_20190313_080000.xlsx: <PERSON><PERSON><PERSON> (41248, 6)\n", "Loaded traindata_20190313_170024.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190329_081100.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190301_080625.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190317_081117.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20190309_081103.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190319_170444.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190319_080437.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190306_080543.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190314_170138.xlsx: <PERSON><PERSON><PERSON> (65224, 6)\n", "Loaded traindata_20190315_080637.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190322_080118.xlsx: <PERSON><PERSON><PERSON> (61922, 6)\n", "Loaded traindata_20190330_081157.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190328_080627.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190326_170118.xlsx: <PERSON><PERSON><PERSON> (41285, 6)\n", "Loaded traindata_20190318_080648.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190324_171350.xlsx: <PERSON><PERSON><PERSON> (38808, 6)\n", "Loaded traindata_20190308_170257.xlsx: <PERSON><PERSON><PERSON> (45412, 6)\n", "Loaded traindata_20190312_170457.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190318_170503.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190324_081051.xlsx: <PERSON><PERSON><PERSON> (38808, 6)\n", "Loaded traindata_20190310_175139.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190329_170509.xlsx: <PERSON><PERSON><PERSON> (52842, 6)\n", "Loaded traindata_20190321_080617.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190326_080008.xlsx: <PERSON><PERSON><PERSON> (47889, 6)\n", "Loaded traindata_20190330_171250.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190316_171445.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190320_080014.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20190320_170437.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190307_170641.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190310_081147.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190303_081229.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190323_081230.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190325_080606.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190307_080627.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190304_170014.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190325_170100.xlsx: <PERSON><PERSON><PERSON> (45412, 6)\n", "Loaded traindata_20190312_080535.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190305_080612.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190309_171118.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190327_080557.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20190305_170006.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190321_170152.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190302_081036.xlsx: <PERSON><PERSON><PERSON> (40459, 6)\n", "Loaded traindata_20190314_081446.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190327_170446.xlsx: <PERSON><PERSON><PERSON> (103197, 6)\n", "Loaded traindata_20190316_081106.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20190317_171150.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190331_161323.xlsx: <PERSON><PERSON><PERSON> (41285, 6)\n", "Loaded traindata_20190301_170009.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190302_170901.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190323_171140.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20190304_080000.xlsx: <PERSON><PERSON><PERSON> (42074, 6)\n", "Loaded traindata_20190322_170236.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190311_170510.xlsx: <PERSON><PERSON><PERSON> (58620, 6)\n", "Loaded traindata_20190331_071129.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190311_080627.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190306_170441.xlsx: <PERSON><PERSON><PERSON> (67701, 6)\n", "Loaded traindata_20190303_171115.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20190315_170152.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190328_170528.xlsx: <PERSON><PERSON><PERSON> (75956, 6)\n", "Loaded traindata_20190308_080554.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190412_070548.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190403_160454.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190426_070344.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190421_070058.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190415_160449.xlsx: <PERSON><PERSON><PERSON> (106499, 6)\n", "Loaded traindata_20190430_070031.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190421_161842.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190401_070257.xlsx: <PERSON><PERSON><PERSON> (58620, 6)\n", "Loaded traindata_20190428_071054.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190413_071424.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190405_070619.xlsx: <PERSON><PERSON><PERSON> (46238, 6)\n", "Loaded traindata_20190423_160047.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190424_074522.xlsx: <PERSON><PERSON><PERSON> (45412, 6)\n", "Loaded traindata_20190402_160000.xlsx: <PERSON><PERSON><PERSON> (45375, 6)\n", "Loaded traindata_20190430_160519.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190419_071442.xlsx: <PERSON><PERSON><PERSON> (41285, 6)\n", "Loaded traindata_20190412_160555.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190410_160209.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190419_160602.xlsx: <PERSON><PERSON><PERSON> (41285, 6)\n", "Loaded traindata_20190423_070637.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190414_071945.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190425_070037.xlsx: <PERSON><PERSON><PERSON> (40459, 6)\n", "Loaded traindata_20190404_070508.xlsx: <PERSON><PERSON><PERSON> (47063, 6)\n", "Loaded traindata_20190429_070016.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190403_070634.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190404_160104.xlsx: <PERSON><PERSON><PERSON> (45412, 6)\n", "Loaded traindata_20190407_072051.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20190407_162149.xlsx: <PERSON><PERSON><PERSON> (52016, 6)\n", "Loaded traindata_20190418_070548.xlsx: <PERSON><PERSON><PERSON> (81734, 6)\n", "Loaded traindata_20190418_160124.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190422_070613.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20190429_160100.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190410_070610.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190420_162202.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190411_160127.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190416_160431.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190417_160506.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20190413_160826.xlsx: <PERSON><PERSON><PERSON> (40459, 6)\n", "Loaded traindata_20190426_160018.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20190405_160231.xlsx: <PERSON><PERSON><PERSON> (51191, 6)\n", "Loaded traindata_20190414_161409.xlsx: <PERSON><PERSON><PERSON> (41285, 6)\n", "Loaded traindata_20190427_161103.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190416_070206.xlsx: <PERSON><PERSON><PERSON> (39634, 6)\n", "Loaded traindata_20190406_072353.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190425_160419.xlsx: <PERSON><PERSON><PERSON> (48714, 6)\n", "Loaded traindata_20190427_071113.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20190424_160152.xlsx: <PERSON><PERSON><PERSON> (56144, 6)\n", "Loaded traindata_20190417_070628.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190420_070015.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190402_070711.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190422_162713.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190406_161354.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190408_070649.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20190415_070600.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190428_161334.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190411_070110.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190401_160117.xlsx: <PERSON><PERSON><PERSON> (46238, 6)\n", "Loaded traindata_20190408_160526.xlsx: <PERSON><PERSON><PERSON> (106499, 6)\n", "Loaded traindata_20190409_070636.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190409_160554.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "\n", "Plotting acceleration-time graphs...\n", "\n", "Extracting features...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:82: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:85: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:82: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:85: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:82: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:85: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:82: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:85: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:82: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:85: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:82: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:85: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:82: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:85: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:82: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:85: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:82: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:85: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:82: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:85: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Feature matrix shape: (7760, 78)\n", "Labels shape: (7760,)\n", "\n", "Computing feature statistics...\n", "NaN values in feature matrix: 3600\n", "Infinite values in feature matrix: 0\n", "Class distribution: Healthy (1): 2880, Unhealthy (0): 4880\n", "Feature statistics saved to img/feature_stats.txt\n", "\n", "Performing RandomizedSearchCV with XGBoost...\n", "Fitting 5 folds for each of 50 candidates, totalling 250 fits\n", "\n", "RandomizedSearchCV Results:\n", "Best parameters: {'subsample': 0.7, 'n_estimators': 500, 'min_child_weight': 1, 'max_depth': 5, 'learning_rate': 0.1, 'gamma': 0, 'colsample_bytree': 0.7}\n", "Best cross-validation score: 0.9189737121769493\n", "\n", "Classification Report:\n", "               precision    recall  f1-score   support\n", "\n", "Unhealthy (0)       0.94      0.94      0.94       976\n", "  Healthy (1)       0.90      0.90      0.90       576\n", "\n", "     accuracy                           0.93      1552\n", "    macro avg       0.92      0.92      0.92      1552\n", " weighted avg       0.93      0.93      0.93      1552\n", "\n"]}], "source": ["import os\n", "import pandas as pd\n", "import numpy as np\n", "from scipy.fft import fft\n", "from scipy.stats import kurtosis, skew, entropy\n", "from sklearn.model_selection import train_test_split, RandomizedSearchCV\n", "from sklearn.preprocessing import StandardScaler\n", "from xgboost import XGBClassifier\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc\n", "import sklearn\n", "import xgboost\n", "import warnings\n", "\n", "# Suppress warnings for cleaner output\n", "warnings.filterwarnings(\"ignore\", category=FutureWarning)\n", "\n", "# Print library versions for debugging\n", "print(f\"scikit-learn version: {sklearn.__version__}\")\n", "print(f\"xgboost version: {xgboost.__version__}\")\n", "\n", "# Set random seed for reproducibility\n", "np.random.seed(42)\n", "\n", "# 1. Data Loading Function\n", "def load_sensor_data(base_path, condition):\n", "    data_list = []\n", "    folder_path = os.path.join(base_path, condition)\n", "    print(f\"\\nLoading {condition} data from {folder_path}...\")\n", "    \n", "    if not os.path.exists(folder_path):\n", "        print(f\"Error: Directory {folder_path} does not exist.\")\n", "        return data_list\n", "    \n", "    for root, _, files in os.walk(folder_path):\n", "        for file in files:\n", "            if file.endswith('.xlsx'):\n", "                file_path = os.path.join(root, file)\n", "                try:\n", "                    df = pd.read_excel(file_path, usecols=range(6))\n", "                    print(f\"Loaded {file}: Shape {df.shape}\")\n", "                    data_list.append(df)\n", "                except Exception as e:\n", "                    print(f\"Error loading {file}: {str(e)}\")\n", "    \n", "    return data_list\n", "\n", "# 2. Advanced Feature Extraction Function\n", "def extract_features(df, sampling_freq=1651, start_sec=2, duration_sec=20, window_sec=0.5):\n", "    start_idx = start_sec * sampling_freq\n", "    end_idx = start_idx + (duration_sec * sampling_freq)\n", "    \n", "    if start_idx >= len(df):\n", "        print(f\"Warning: Start index exceeds data length for file with {len(df)} samples.\")\n", "        return pd.DataFrame()\n", "    if end_idx > len(df):\n", "        print(f\"Warning: Data truncated from {len(df)} to {end_idx} samples.\")\n", "        end_idx = len(df)\n", "    \n", "    df_chunk = df.iloc[start_idx:end_idx]\n", "    n_samples = len(df_chunk)\n", "    window_size = int(window_sec * sampling_freq)\n", "    n_windows = n_samples // window_size\n", "    \n", "    if n_windows == 0:\n", "        print(f\"Warning: No complete windows available in chunk of {n_samples} samples.\")\n", "        return pd.DataFrame()\n", "    \n", "    features_list = []\n", "    for sensor in range(6):\n", "        sensor_data = df_chunk.iloc[:, sensor]\n", "        sensor_features = []\n", "        \n", "        for i in range(n_windows):\n", "            chunk = sensor_data[i*window_size:(i+1)*window_size]\n", "            \n", "            # Time-domain features\n", "            rms = np.sqrt(np.mean(chunk**2))\n", "            peak_to_peak = np.max(chunk) - np.min(chunk)\n", "            zero_crossings = np.sum(np.diff(np.sign(chunk)) != 0) / 2\n", "            kurt = kurtosis(chunk, nan_policy='omit')\n", "            crest_factor = np.max(np.abs(chunk)) / rms if rms != 0 else 0\n", "            mad = np.mean(np.abs(chunk - np.mean(chunk)))\n", "            skw = skew(chunk, nan_policy='omit')\n", "            energy = np.sum(chunk**2)\n", "            \n", "            # Frequency-domain features\n", "            fft_vals = fft(chunk.to_numpy())\n", "            fft_magnitude = np.abs(fft_vals)[:window_size//2]\n", "            freqs = np.fft.fftfreq(window_size, 1/sampling_freq)[:window_size//2]\n", "            dominant_freq = freqs[np.argmax(fft_magnitude)] if fft_magnitude.size > 0 else 0\n", "            spectral_power = np.sum(fft_magnitude**2) / window_size if fft_magnitude.size > 0 else 0\n", "            fft_magnitude_sorted = np.sort(fft_magnitude)[::-1]\n", "            second_dominant_freq = freqs[np.argsort(fft_magnitude)[-2]] if len(freqs) > 1 else 0\n", "            spectral_entropy = entropy(fft_magnitude / np.sum(fft_magnitude)) if np.sum(fft_magnitude) != 0 else 0\n", "            spectral_centroid = np.sum(freqs * fft_magnitude) / np.sum(fft_magnitude) if np.sum(fft_magnitude) != 0 else 0\n", "            \n", "            feat = {\n", "                f'sensor_{sensor}_rms': rms,\n", "                f'sensor_{sensor}_peak_to_peak': peak_to_peak,\n", "                f'sensor_{sensor}_zero_crossings': zero_crossings,\n", "                f'sensor_{sensor}_kurtosis': kurt,\n", "                f'sensor_{sensor}_crest_factor': crest_factor,\n", "                f'sensor_{sensor}_mad': mad,\n", "                f'sensor_{sensor}_skewness': skw,\n", "                f'sensor_{sensor}_energy': energy,\n", "                f'sensor_{sensor}_dominant_freq': dominant_freq,\n", "                f'sensor_{sensor}_second_dominant_freq': second_dominant_freq,\n", "                f'sensor_{sensor}_spectral_power': spectral_power,\n", "                f'sensor_{sensor}_spectral_entropy': spectral_entropy,\n", "                f'sensor_{sensor}_spectral_centroid': spectral_centroid\n", "            }\n", "            sensor_features.append(feat)\n", "        \n", "        features_list.append(pd.DataFrame(sensor_features))\n", "    \n", "    return pd.concat(features_list, axis=1)\n", "\n", "# 3. Feature Statistics Function\n", "def compute_feature_statistics(X, y, save_path=\"img/feature_stats.txt\"):\n", "    print(\"\\nComputing feature statistics...\")\n", "    \n", "    # Check for missing or invalid values\n", "    nan_count = X.isna().sum().sum()\n", "    inf_count = np.isinf(X).sum().sum()\n", "    print(f\"NaN values in feature matrix: {nan_count}\")\n", "    print(f\"Infinite values in feature matrix: {inf_count}\")\n", "    \n", "    # Replace NaNs with median and infinities with max/min\n", "    X_clean = <PERSON>.fillna(X.median()).replace([np.inf, -np.inf], [X.max(), X.min()])\n", "    \n", "    # Class distribution\n", "    healthy_count = sum(y == 1)\n", "    unhealthy_count = sum(y == 0)\n", "    print(f\"Class distribution: Healthy (1): {healthy_count}, Unhealthy (0): {unhealthy_count}\")\n", "    \n", "    # Summary statistics\n", "    stats = X_clean.describe().T[['mean', 'std', 'min', 'max']]\n", "    stats['variance'] = stats['std'] ** 2\n", "    \n", "    # Group by class\n", "    healthy_stats = X_clean[y == 1].describe().T[['mean', 'std']]\n", "    unhealthy_stats = X_clean[y == 0].describe().T[['mean', 'std']]\n", "    stats = stats.join(healthy_stats.rename(columns={'mean': 'healthy_mean', 'std': 'healthy_std'}))\n", "    stats = stats.join(unhealthy_stats.rename(columns={'mean': 'unhealthy_mean', 'std': 'unhealthy_std'}))\n", "    \n", "    # Save to file\n", "    os.makedirs(os.path.dirname(save_path), exist_ok=True)\n", "    with open(save_path, 'w') as f:\n", "        f.write(\"Feature Statistics\\n\")\n", "        f.write(f\"NaN values: {nan_count}\\n\")\n", "        f.write(f\"Infinite values: {inf_count}\\n\")\n", "        f.write(f\"Class distribution: Healthy: {healthy_count}, Unhealthy: {unhealthy_count}\\n\\n\")\n", "        f.write(stats.to_string())\n", "    \n", "    print(f\"Feature statistics saved to {save_path}\")\n", "    return stats\n", "\n", "# 4. Visualization Functions\n", "def plot_acceleration_time(healthy_data, unhealthy_data, sampling_freq=1651, n_samples=3):\n", "    healthy_sample = healthy_data[:n_samples]\n", "    unhealthy_sample = unhealthy_data[:n_samples]\n", "    max_samples = int(25 * sampling_freq)\n", "    \n", "    for i, df in enumerate(healthy_sample):\n", "        n_samples = min(len(df), max_samples)\n", "        time = np.arange(0, n_samples/sampling_freq, 1/sampling_freq)\n", "        plt.figure(figsize=(3.5, 3))\n", "        df_plot = df.iloc[:n_samples]\n", "        for sensor in range(6):\n", "            plt.plot(time, df_plot.iloc[:, sensor], label=f'Sensor {sensor}', alpha=0.7)\n", "        plt.title(f'<PERSON><PERSON> Sample {i+1}', fontsize=10)\n", "        plt.xlabel('Time (s)', fontsize=8)\n", "        plt.ylabel('Acceleration', fontsize=8)\n", "        plt.legend(fontsize=7)\n", "        plt.grid(True)\n", "        plt.tick_params(axis='both', labelsize=7)\n", "        plt.tight_layout()\n", "        plt.savefig(f\"img/healthy_time_{i+1}.png\", dpi=300, bbox_inches='tight')\n", "        plt.close()\n", "    \n", "    for i, df in enumerate(unhealthy_sample):\n", "        n_samples = min(len(df), max_samples)\n", "        time = np.arange(0, n_samples/sampling_freq, 1/sampling_freq)\n", "        plt.figure(figsize=(3.5, 3))\n", "        df_plot = df.iloc[:n_samples]\n", "        for sensor in range(6):\n", "            plt.plot(time, df_plot.iloc[:, sensor], label=f'Sensor {sensor}', alpha=0.7)\n", "        plt.title(f'Unhealthy Sample {i+1}', fontsize=10)\n", "        plt.xlabel('Time (s)', fontsize=8)\n", "        plt.ylabel('Acceleration', fontsize=8)\n", "        plt.legend(fontsize=7)\n", "        plt.grid(True)\n", "        plt.tick_params(axis='both', labelsize=7)\n", "        plt.tight_layout()\n", "        plt.savefig(f\"img/unhealthy_time_{i+1}.png\", dpi=300, bbox_inches='tight')\n", "        plt.close()\n", "\n", "def plot_feature_distribution(X, y):\n", "    features_df = pd.DataFrame({\n", "        \"RMS\": X[\"sensor_0_rms\"],\n", "        \"Kurtosis\": X[\"sensor_0_kurtosis\"],\n", "        \"Spectral_Entropy\": X[\"sensor_0_spectral_entropy\"],\n", "        \"Class\": [\"Healthy\" if label == 1 else \"Unhealthy\" for label in y]\n", "    })\n", "    \n", "    plt.figure(figsize=(3.5, 3))\n", "    sns.violinplot(data=features_df.melt(id_vars=\"Class\"), x=\"variable\", y=\"value\", hue=\"Class\", split=True)\n", "    plt.xlabel(\"Feature\", fontsize=8)\n", "    plt.ylabel(\"Feature Value\", fontsize=8)\n", "    plt.title(\"Feature Distributions\", fontsize=10)\n", "    plt.tick_params(axis='both', labelsize=7)\n", "    plt.legend(title=\"Class\", fontsize=7)\n", "    plt.tight_layout()\n", "    plt.savefig(\"img/feature_distribution.png\", dpi=300, bbox_inches='tight')\n", "    plt.close()\n", "\n", "def plot_feature_trends(healthy_data, unhealthy_data):\n", "    if not healthy_data or not unhealthy_data:\n", "        print(\"Warning: No data available for feature trends plot.\")\n", "        return\n", "    \n", "    healthy_features = extract_features(healthy_data[0])\n", "    unhealthy_features = extract_features(unhealthy_data[0])\n", "    \n", "    if healthy_features.empty or unhealthy_features.empty:\n", "        print(\"Warning: Feature extraction failed for trends plot.\")\n", "        return\n", "    \n", "    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(3.5, 3), sharex=True)\n", "    windows = range(len(healthy_features))\n", "    ax1.plot(windows, healthy_features[\"sensor_0_rms\"], label=\"Healthy\")\n", "    ax1.plot(windows, unhealthy_features[\"sensor_0_rms\"], label=\"Unhealthy\")\n", "    ax2.plot(windows, healthy_features[\"sensor_0_spectral_power\"], label=\"Healthy\")\n", "    ax2.plot(windows, unhealthy_features[\"sensor_0_spectral_power\"], label=\"Unhealthy\")\n", "    ax1.set_title(\"RMS Over Windows\", fontsize=10)\n", "    ax2.set_title(\"Spectral Power Over Windows\", fontsize=10)\n", "    ax1.set_ylabel(\"RMS\", fontsize=8)\n", "    ax2.set_ylabel(\"Spectral Power\", fontsize=8)\n", "    ax2.set_xlabel(\"Window Index\", fontsize=8)\n", "    ax1.tick_params(axis='both', labelsize=7)\n", "    ax2.tick_params(axis='both', labelsize=7)\n", "    ax1.legend(fontsize=7)\n", "    ax2.legend(fontsize=7)\n", "    plt.tight_layout()\n", "    plt.savefig(\"img/feature_trends.png\", dpi=300, bbox_inches='tight')\n", "    plt.close()\n", "\n", "# 5. Main Processing Pipeline\n", "def main():\n", "    base_path = \"/Users/<USER>/Documents/bridge_sensor/\"\n", "    os.makedirs(\"img\", exist_ok=True)\n", "    \n", "    # Load data\n", "    healthy_data = load_sensor_data(base_path, \"healthy\")\n", "    unhealthy_data = load_sensor_data(base_path, \"unhealthy\")\n", "    \n", "    if not healthy_data and not unhealthy_data:\n", "        print(\"Error: No data loaded from either healthy or unhealthy folders.\")\n", "        return\n", "    \n", "    # Visualize time-series\n", "    print(\"\\nPlotting acceleration-time graphs...\")\n", "    plot_acceleration_time(healthy_data, unhealthy_data, n_samples=3)\n", "    \n", "    # Extract features\n", "    print(\"\\nExtracting features...\")\n", "    X_healthy = []\n", "    y_healthy = []\n", "    for df in healthy_data:\n", "        features = extract_features(df)\n", "        if not features.empty:\n", "            X_healthy.append(features)\n", "            y_healthy.extend([1] * len(features))\n", "    \n", "    X_unhealthy = []\n", "    y_unhealthy = []\n", "    for df in unhealthy_data:\n", "        features = extract_features(df)\n", "        if not features.empty:\n", "            X_unhealthy.append(features)\n", "            y_unhealthy.extend([0] * len(features))\n", "    \n", "    if not X_healthy and not X_unhealthy:\n", "        print(\"Error: No valid data extracted from any files.\")\n", "        return\n", "    \n", "    X = pd.concat(X_healthy + X_unhealthy, axis=0)\n", "    y = np.array(y_healthy + y_unhealthy)\n", "    \n", "    print(f\"\\nFeature matrix shape: {X.shape}\")\n", "    print(f\"Labels shape: {y.shape}\")\n", "    \n", "    # Compute feature statistics\n", "    stats = compute_feature_statistics(X, y)\n", "    \n", "    # Plot feature distribution and trends\n", "    plot_feature_distribution(X, y)\n", "    plot_feature_trends(healthy_data, unhealthy_data)\n", "    \n", "    # Split data\n", "    X_train, X_test, y_train, y_test = train_test_split(\n", "        X, y, test_size=0.2, random_state=42, stratify=y\n", "    )\n", "    \n", "    # Scale features\n", "    scaler = StandardScaler()\n", "    X_train_scaled = scaler.fit_transform(X_train)\n", "    X_test_scaled = scaler.transform(X_test)\n", "    \n", "    # XGBoost with RandomizedSearchCV\n", "    print(\"\\nPerforming RandomizedSearchCV with XGBoost...\")\n", "    xgb = XGBClassifier(\n", "        random_state=42,\n", "        eval_metric='logloss',\n", "        scale_pos_weight=sum(y==0)/sum(y==1)\n", "    )\n", "    \n", "    param_dist = {\n", "        'max_depth': [3, 5, 7, 9],\n", "        'learning_rate': [0.05, 0.1, 0.15, 0.2, 0.3],\n", "        'n_estimators': [100, 200, 300, 400, 500],\n", "        'subsample': [0.6, 0.7, 0.8, 0.9, 1.0],\n", "        'colsample_bytree': [0.6, 0.7, 0.8, 0.9, 1.0],\n", "        'gamma': [0, 0.1, 0.2, 0.3],\n", "        'min_child_weight': [1, 3, 5]\n", "    }\n", "    \n", "    random_search = RandomizedSearchCV(\n", "        estimator=xgb,\n", "        param_distributions=param_dist,\n", "        n_iter=50,\n", "        cv=5,\n", "        scoring='accuracy',\n", "        n_jobs=-1,\n", "        verbose=1,\n", "        random_state=42\n", "    )\n", "    \n", "    try:\n", "        random_search.fit(X_train_scaled, y_train)\n", "    except Exception as e:\n", "        print(f\"Error during RandomizedSearchCV fit: {str(e)}\")\n", "        print(\"Try updating xgboost (`pip install --upgrade xgboost`) or downgrading scikit-learn (`pip install scikit-learn==1.4.2`)\")\n", "        return\n", "    \n", "    print(\"\\nRandomizedSearchCV Results:\")\n", "    print(\"Best parameters:\", random_search.best_params_)\n", "    print(\"Best cross-validation score:\", random_search.best_score_)\n", "    \n", "    # Evaluate the best model\n", "    best_model = random_search.best_estimator_\n", "    y_pred = best_model.predict(X_test_scaled)\n", "    \n", "    print(\"\\nClassification Report:\")\n", "    print(classification_report(y_test, y_pred, target_names=['Unhealthy (0)', 'Healthy (1)']))\n", "    \n", "    # Plot confusion matrix\n", "    plt.figure(figsize=(3.5, 3))\n", "    cm = confusion_matrix(y_test, y_pred)\n", "    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \n", "                xticklabels=['Unhealthy', 'Healthy'], \n", "                yticklabels=['Unhealthy', 'Healthy'])\n", "    plt.title('Confusion Matrix', fontsize=10)\n", "    plt.ylabel('True Label', fontsize=8)\n", "    plt.xlabel('Predicted Label', fontsize=8)\n", "    plt.tick_params(axis='both', labelsize=7)\n", "    plt.tight_layout()\n", "    plt.savefig(\"img/confusion_matrix.png\", dpi=300, bbox_inches='tight')\n", "    plt.close()\n", "    \n", "    # Plot ROC curve\n", "    y_pred_proba = best_model.predict_proba(X_test_scaled)[:, 1]\n", "    fpr, tpr, _ = roc_curve(y_test, y_pred_proba)\n", "    roc_auc = auc(fpr, tpr)\n", "    \n", "    plt.figure(figsize=(3.5, 3))\n", "    plt.plot(fpr, tpr, color='darkorange', lw=1, \n", "             label=f'ROC curve (AUC = {roc_auc:.2f})')\n", "    plt.plot([0, 1], [0, 1], color='navy', lw=1, linestyle='--')\n", "    plt.xlim([0.0, 1.0])\n", "    plt.ylim([0.0, 1.05])\n", "    plt.xlabel('False Positive Rate', fontsize=8)\n", "    plt.ylabel('True Positive Rate', fontsize=8)\n", "    plt.title('ROC Curve', fontsize=10)\n", "    plt.legend(loc=\"lower right\", fontsize=7)\n", "    plt.tick_params(axis='both', labelsize=7)\n", "    plt.tight_layout()\n", "    plt.savefig(\"img/roc_curve.png\", dpi=300, bbox_inches='tight')\n", "    plt.close()\n", "    \n", "    # Plot feature importance\n", "    plt.figure(figsize=(3.5, 3))\n", "    feat_importance = pd.DataFrame({\n", "        'feature': X.columns,\n", "        'importance': best_model.feature_importances_\n", "    })\n", "    feat_importance = feat_importance.sort_values('importance', ascending=False)\n", "    sns.barplot(x='importance', y='feature', data=feat_importance.head(10))\n", "    plt.title('Top 10 Feature Importances', fontsize=10)\n", "    plt.xlabel('Importance Score', fontsize=8)\n", "    plt.ylabel('Feature', fontsize=8)\n", "    plt.tick_params(axis='both', labelsize=7)\n", "    plt.tight_layout()\n", "    plt.savefig(\"img/feature_importance.png\", dpi=300, bbox_inches='tight')\n", "    plt.close()\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": 3, "id": "00094530", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: xgboost in /Users/<USER>/dl-env/lib/python3.10/site-packages (2.1.3)\n", "Collecting xgboost\n", "  Downloading xgboost-3.0.0-py3-none-macosx_12_0_arm64.whl.metadata (2.1 kB)\n", "Requirement already satisfied: numpy in /Users/<USER>/dl-env/lib/python3.10/site-packages (from xgboost) (1.26.4)\n", "Requirement already satisfied: scipy in /Users/<USER>/dl-env/lib/python3.10/site-packages (from xgboost) (1.14.1)\n", "Downloading xgboost-3.0.0-py3-none-macosx_12_0_arm64.whl (2.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.0/2.0 MB\u001b[0m \u001b[31m17.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: xgboost\n", "  Attempting uninstall: xgboost\n", "    Found existing installation: xgboost 2.1.3\n", "    Uninstalling xgboost-2.1.3:\n", "      Successfully uninstalled xgboost-2.1.3\n", "Successfully installed xgboost-3.0.0\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m25.0.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install --upgrade xgboost"]}, {"cell_type": "code", "execution_count": 6, "id": "9290ba86", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting tabulate\n", "  Using cached tabulate-0.9.0-py3-none-any.whl.metadata (34 kB)\n", "Using cached tabulate-0.9.0-py3-none-any.whl (35 kB)\n", "Installing collected packages: tabulate\n", "Successfully installed tabulate-0.9.0\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m25.0.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install tabulate"]}, {"cell_type": "code", "execution_count": 7, "id": "4550a925", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["scikit-learn version: 1.6.1\n", "xgboost version: 3.0.0\n", "\n", "Loading healthy data from /Users/<USER>/Documents/bridge_sensor/healthy...\n", "Loaded traindata_20191028_170815.xlsx: <PERSON><PERSON><PERSON> (69352, 6)\n", "Loaded traindata_20191031_080607.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20191029_080435.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191027_171117.xlsx: <PERSON><PERSON><PERSON> (38808, 6)\n", "Loaded traindata_20191026_071135.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20191026_161129.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20191030_170103.xlsx: <PERSON><PERSON><PERSON> (48714, 6)\n", "Loaded traindata_20191027_081146.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20191030_080606.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191029_170232.xlsx: <PERSON><PERSON><PERSON> (47889, 6)\n", "Loaded traindata_20191028_081019.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20191031_170250.xlsx: <PERSON><PERSON><PERSON> (47889, 6)\n", "Loaded traindata_20191025_160516.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20191107_080636.xlsx: <PERSON><PERSON><PERSON> (46238, 6)\n", "Loaded traindata_20191102_171055.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20191115_080506.xlsx: <PERSON><PERSON><PERSON> (78432, 6)\n", "Loaded traindata_20191111_170435.xlsx: <PERSON><PERSON><PERSON> (40459, 6)\n", "Loaded traindata_20191123_080939.xlsx: <PERSON><PERSON><PERSON> (40459, 6)\n", "Loaded traindata_20191128_170246.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20191126_080220.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20191109_081204.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20191123_171140.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20191130_171042.xlsx: <PERSON><PERSON><PERSON> (67701, 6)\n", "Loaded traindata_20191118_080318.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20191122_080649.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191112_170053.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20191119_080043.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191128_080619.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191109_171114.xlsx: <PERSON><PERSON><PERSON> (38808, 6)\n", "Loaded traindata_20191105_170114.xlsx: <PERSON><PERSON><PERSON> (39634, 6)\n", "Loaded traindata_20191108_170517.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20191110_171637.xlsx: <PERSON><PERSON><PERSON> (39634, 6)\n", "Loaded traindata_20191111_081134.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20191120_081113.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191125_080555.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191115_170437.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191116_081423.xlsx: <PERSON><PERSON><PERSON> (41285, 6)\n", "Loaded traindata_20191114_080031.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20191117_171711.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20191104_170038.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20191117_080019.xlsx: <PERSON><PERSON><PERSON> (45412, 6)\n", "Loaded traindata_20191112_080022.xlsx: <PERSON><PERSON><PERSON> (45412, 6)\n", "Loaded traindata_20191129_080556.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20191101_171345.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20191126_170240.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20191121_080135.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20191124_081359.xlsx: <PERSON><PERSON><PERSON> (39634, 6)\n", "Loaded traindata_20191127_080407.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191102_081127.xlsx: <PERSON><PERSON><PERSON> (51191, 6)\n", "Loaded traindata_20191122_170414.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20191120_170142.xlsx: <PERSON><PERSON><PERSON> (52016, 6)\n", "Loaded traindata_20191104_080120.xlsx: <PERSON><PERSON><PERSON> (47063, 6)\n", "Loaded traindata_20191129_170000.xlsx: <PERSON><PERSON><PERSON> (70966, 6)\n", "Loaded traindata_20191116_171052.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20191107_170000.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20191108_080624.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191110_081640.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20191124_171140.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20191119_170235.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20191118_170302.xlsx: <PERSON><PERSON><PERSON> (40459, 6)\n", "Loaded traindata_20191130_081116.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20191103_171044.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20191113_081216.xlsx: <PERSON><PERSON><PERSON> (57795, 6)\n", "Loaded traindata_20191127_170700.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20191103_081124.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20191125_170057.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20191106_080626.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20191114_170508.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20191121_170024.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20191101_081130.xlsx: <PERSON><PERSON><PERSON> (38808, 6)\n", "Loaded traindata_20191106_171005.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20191113_170643.xlsx: <PERSON><PERSON><PERSON> (65224, 6)\n", "\n", "Loading unhealthy data from /Users/<USER>/Documents/bridge_sensor/unhealthy...\n", "Loaded traindata_20190313_080000.xlsx: <PERSON><PERSON><PERSON> (41248, 6)\n", "Loaded traindata_20190313_170024.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190329_081100.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190301_080625.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190317_081117.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20190309_081103.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190319_170444.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190319_080437.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190306_080543.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190314_170138.xlsx: <PERSON><PERSON><PERSON> (65224, 6)\n", "Loaded traindata_20190315_080637.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190322_080118.xlsx: <PERSON><PERSON><PERSON> (61922, 6)\n", "Loaded traindata_20190330_081157.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190328_080627.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190326_170118.xlsx: <PERSON><PERSON><PERSON> (41285, 6)\n", "Loaded traindata_20190318_080648.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190324_171350.xlsx: <PERSON><PERSON><PERSON> (38808, 6)\n", "Loaded traindata_20190308_170257.xlsx: <PERSON><PERSON><PERSON> (45412, 6)\n", "Loaded traindata_20190312_170457.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190318_170503.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190324_081051.xlsx: <PERSON><PERSON><PERSON> (38808, 6)\n", "Loaded traindata_20190310_175139.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190329_170509.xlsx: <PERSON><PERSON><PERSON> (52842, 6)\n", "Loaded traindata_20190321_080617.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190326_080008.xlsx: <PERSON><PERSON><PERSON> (47889, 6)\n", "Loaded traindata_20190330_171250.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190316_171445.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190320_080014.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20190320_170437.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190307_170641.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190310_081147.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190303_081229.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190323_081230.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190325_080606.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190307_080627.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190304_170014.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190325_170100.xlsx: <PERSON><PERSON><PERSON> (45412, 6)\n", "Loaded traindata_20190312_080535.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190305_080612.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190309_171118.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190327_080557.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20190305_170006.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190321_170152.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190302_081036.xlsx: <PERSON><PERSON><PERSON> (40459, 6)\n", "Loaded traindata_20190314_081446.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190327_170446.xlsx: <PERSON><PERSON><PERSON> (103197, 6)\n", "Loaded traindata_20190316_081106.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20190317_171150.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190331_161323.xlsx: <PERSON><PERSON><PERSON> (41285, 6)\n", "Loaded traindata_20190301_170009.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190302_170901.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190323_171140.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20190304_080000.xlsx: <PERSON><PERSON><PERSON> (42074, 6)\n", "Loaded traindata_20190322_170236.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190311_170510.xlsx: <PERSON><PERSON><PERSON> (58620, 6)\n", "Loaded traindata_20190331_071129.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190311_080627.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190306_170441.xlsx: <PERSON><PERSON><PERSON> (67701, 6)\n", "Loaded traindata_20190303_171115.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20190315_170152.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190328_170528.xlsx: <PERSON><PERSON><PERSON> (75956, 6)\n", "Loaded traindata_20190308_080554.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190412_070548.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190403_160454.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190426_070344.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190421_070058.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190415_160449.xlsx: <PERSON><PERSON><PERSON> (106499, 6)\n", "Loaded traindata_20190430_070031.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190421_161842.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190401_070257.xlsx: <PERSON><PERSON><PERSON> (58620, 6)\n", "Loaded traindata_20190428_071054.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190413_071424.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190405_070619.xlsx: <PERSON><PERSON><PERSON> (46238, 6)\n", "Loaded traindata_20190423_160047.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190424_074522.xlsx: <PERSON><PERSON><PERSON> (45412, 6)\n", "Loaded traindata_20190402_160000.xlsx: <PERSON><PERSON><PERSON> (45375, 6)\n", "Loaded traindata_20190430_160519.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190419_071442.xlsx: <PERSON><PERSON><PERSON> (41285, 6)\n", "Loaded traindata_20190412_160555.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190410_160209.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190419_160602.xlsx: <PERSON><PERSON><PERSON> (41285, 6)\n", "Loaded traindata_20190423_070637.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190414_071945.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190425_070037.xlsx: <PERSON><PERSON><PERSON> (40459, 6)\n", "Loaded traindata_20190404_070508.xlsx: <PERSON><PERSON><PERSON> (47063, 6)\n", "Loaded traindata_20190429_070016.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190403_070634.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190404_160104.xlsx: <PERSON><PERSON><PERSON> (45412, 6)\n", "Loaded traindata_20190407_072051.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20190407_162149.xlsx: <PERSON><PERSON><PERSON> (52016, 6)\n", "Loaded traindata_20190418_070548.xlsx: <PERSON><PERSON><PERSON> (81734, 6)\n", "Loaded traindata_20190418_160124.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190422_070613.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20190429_160100.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190410_070610.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190420_162202.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190411_160127.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190416_160431.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190417_160506.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20190413_160826.xlsx: <PERSON><PERSON><PERSON> (40459, 6)\n", "Loaded traindata_20190426_160018.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20190405_160231.xlsx: <PERSON><PERSON><PERSON> (51191, 6)\n", "Loaded traindata_20190414_161409.xlsx: <PERSON><PERSON><PERSON> (41285, 6)\n", "Loaded traindata_20190427_161103.xlsx: <PERSON><PERSON><PERSON> (37983, 6)\n", "Loaded traindata_20190416_070206.xlsx: <PERSON><PERSON><PERSON> (39634, 6)\n", "Loaded traindata_20190406_072353.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190425_160419.xlsx: <PERSON><PERSON><PERSON> (48714, 6)\n", "Loaded traindata_20190427_071113.xlsx: <PERSON><PERSON><PERSON> (37157, 6)\n", "Loaded traindata_20190424_160152.xlsx: <PERSON><PERSON><PERSON> (56144, 6)\n", "Loaded traindata_20190417_070628.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190420_070015.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190402_070711.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190422_162713.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190406_161354.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190408_070649.xlsx: <PERSON><PERSON><PERSON> (44587, 6)\n", "Loaded traindata_20190415_070600.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190428_161334.xlsx: <PERSON><PERSON><PERSON> (42110, 6)\n", "Loaded traindata_20190411_070110.xlsx: <PERSON><PERSON><PERSON> (42936, 6)\n", "Loaded traindata_20190401_160117.xlsx: <PERSON><PERSON><PERSON> (46238, 6)\n", "Loaded traindata_20190408_160526.xlsx: <PERSON><PERSON><PERSON> (106499, 6)\n", "Loaded traindata_20190409_070636.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "Loaded traindata_20190409_160554.xlsx: <PERSON><PERSON><PERSON> (43761, 6)\n", "\n", "Plotting acceleration-time graphs...\n", "\n", "Extracting features...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:83: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:86: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:83: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:86: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:83: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:86: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:83: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:86: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:83: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:86: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:83: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:86: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:83: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:86: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:83: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:86: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:83: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:86: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:83: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  kurt = kurtosis(chunk, nan_policy='omit')\n", "/var/folders/xp/hk3smldd0s13_p573l152qhm0000gn/T/ipykernel_56796/**********.py:86: SmallSampleWarning: After omitting NaNs, one or more sample arguments is too small; all returned values will be NaN. See documentation for sample size requirements.\n", "  skw = skew(chunk, nan_policy='omit')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Feature matrix shape: (7760, 78)\n", "Labels shape: (7760,)\n", "\n", "Computing feature statistics...\n", "NaN values in feature matrix: 3600\n", "Infinite values in feature matrix: 0\n", "Class distribution: Healthy (1): 2880, Unhealthy (0): 4880\n", "Feature statistics saved to img/feature_stats.txt\n", "\n", "Performing RandomizedSearchCV with XGBoost...\n", "Fitting 5 folds for each of 50 candidates, totalling 250 fits\n", "\n", "RandomizedSearchCV Results:\n", "Best parameters: {'subsample': 0.7, 'n_estimators': 500, 'min_child_weight': 1, 'max_depth': 5, 'learning_rate': 0.1, 'gamma': 0, 'colsample_bytree': 0.7}\n", "Best cross-validation score: 0.9189737121769493\n", "\n", "Classification Metrics Table:\n", "+-----------+-------------+----------+------------+-----------+\n", "| Class     |   Precision |   Recall |   F1-Score |   Support |\n", "+===========+=============+==========+============+===========+\n", "| Unhealthy |       0.94  |    0.942 |      0.941 |       976 |\n", "+-----------+-------------+----------+------------+-----------+\n", "| Healthy   |       0.901 |    0.898 |      0.899 |       576 |\n", "+-----------+-------------+----------+------------+-----------+\n", "| Overall   |       0.925 |  nan     |    nan     |       nan |\n", "+-----------+-------------+----------+------------+-----------+\n", "Metrics table saved to img/metrics_table.csv\n"]}], "source": ["import os\n", "import pandas as pd\n", "import numpy as np\n", "from scipy.fft import fft\n", "from scipy.stats import kurtosis, skew, entropy\n", "from sklearn.model_selection import train_test_split, RandomizedSearchCV\n", "from sklearn.preprocessing import StandardScaler\n", "from xgboost import XGBClassifier\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc\n", "import sklearn\n", "import xgboost\n", "import warnings\n", "from tabulate import tabulate\n", "\n", "# Suppress warnings for cleaner output\n", "warnings.filterwarnings(\"ignore\", category=FutureWarning)\n", "\n", "# Print library versions for debugging\n", "print(f\"scikit-learn version: {sklearn.__version__}\")\n", "print(f\"xgboost version: {xgboost.__version__}\")\n", "\n", "# Set random seed for reproducibility\n", "np.random.seed(42)\n", "\n", "# 1. Data Loading Function\n", "def load_sensor_data(base_path, condition):\n", "    data_list = []\n", "    folder_path = os.path.join(base_path, condition)\n", "    print(f\"\\nLoading {condition} data from {folder_path}...\")\n", "    \n", "    if not os.path.exists(folder_path):\n", "        print(f\"Error: Directory {folder_path} does not exist.\")\n", "        return data_list\n", "    \n", "    for root, _, files in os.walk(folder_path):\n", "        for file in files:\n", "            if file.endswith('.xlsx'):\n", "                file_path = os.path.join(root, file)\n", "                try:\n", "                    df = pd.read_excel(file_path, usecols=range(6))\n", "                    print(f\"Loaded {file}: Shape {df.shape}\")\n", "                    data_list.append(df)\n", "                except Exception as e:\n", "                    print(f\"Error loading {file}: {str(e)}\")\n", "    \n", "    return data_list\n", "\n", "# 2. Advanced Feature Extraction Function\n", "def extract_features(df, sampling_freq=1651, start_sec=2, duration_sec=20, window_sec=0.5):\n", "    start_idx = start_sec * sampling_freq\n", "    end_idx = start_idx + (duration_sec * sampling_freq)\n", "    \n", "    if start_idx >= len(df):\n", "        print(f\"Warning: Start index exceeds data length for file with {len(df)} samples.\")\n", "        return pd.DataFrame()\n", "    if end_idx > len(df):\n", "        print(f\"Warning: Data truncated from {len(df)} to {end_idx} samples.\")\n", "        end_idx = len(df)\n", "    \n", "    df_chunk = df.iloc[start_idx:end_idx]\n", "    n_samples = len(df_chunk)\n", "    window_size = int(window_sec * sampling_freq)\n", "    n_windows = n_samples // window_size\n", "    \n", "    if n_windows == 0:\n", "        print(f\"Warning: No complete windows available in chunk of {n_samples} samples.\")\n", "        return pd.DataFrame()\n", "    \n", "    features_list = []\n", "    for sensor in range(6):\n", "        sensor_data = df_chunk.iloc[:, sensor]\n", "        sensor_features = []\n", "        \n", "        for i in range(n_windows):\n", "            chunk = sensor_data[i*window_size:(i+1)*window_size]\n", "            \n", "            # Time-domain features\n", "            rms = np.sqrt(np.mean(chunk**2))\n", "            peak_to_peak = np.max(chunk) - np.min(chunk)\n", "            zero_crossings = np.sum(np.diff(np.sign(chunk)) != 0) / 2\n", "            kurt = kurtosis(chunk, nan_policy='omit')\n", "            crest_factor = np.max(np.abs(chunk)) / rms if rms != 0 else 0\n", "            mad = np.mean(np.abs(chunk - np.mean(chunk)))\n", "            skw = skew(chunk, nan_policy='omit')\n", "            energy = np.sum(chunk**2)\n", "            \n", "            # Frequency-domain features\n", "            fft_vals = fft(chunk.to_numpy())\n", "            fft_magnitude = np.abs(fft_vals)[:window_size//2]\n", "            freqs = np.fft.fftfreq(window_size, 1/sampling_freq)[:window_size//2]\n", "            dominant_freq = freqs[np.argmax(fft_magnitude)] if fft_magnitude.size > 0 else 0\n", "            spectral_power = np.sum(fft_magnitude**2) / window_size if fft_magnitude.size > 0 else 0\n", "            fft_magnitude_sorted = np.sort(fft_magnitude)[::-1]\n", "            second_dominant_freq = freqs[np.argsort(fft_magnitude)[-2]] if len(freqs) > 1 else 0\n", "            spectral_entropy = entropy(fft_magnitude / np.sum(fft_magnitude)) if np.sum(fft_magnitude) != 0 else 0\n", "            spectral_centroid = np.sum(freqs * fft_magnitude) / np.sum(fft_magnitude) if np.sum(fft_magnitude) != 0 else 0\n", "            \n", "            feat = {\n", "                f'sensor_{sensor}_rms': rms,\n", "                f'sensor_{sensor}_peak_to_peak': peak_to_peak,\n", "                f'sensor_{sensor}_zero_crossings': zero_crossings,\n", "                f'sensor_{sensor}_kurtosis': kurt,\n", "                f'sensor_{sensor}_crest_factor': crest_factor,\n", "                f'sensor_{sensor}_mad': mad,\n", "                f'sensor_{sensor}_skewness': skw,\n", "                f'sensor_{sensor}_energy': energy,\n", "                f'sensor_{sensor}_dominant_freq': dominant_freq,\n", "                f'sensor_{sensor}_second_dominant_freq': second_dominant_freq,\n", "                f'sensor_{sensor}_spectral_power': spectral_power,\n", "                f'sensor_{sensor}_spectral_entropy': spectral_entropy,\n", "                f'sensor_{sensor}_spectral_centroid': spectral_centroid\n", "            }\n", "            sensor_features.append(feat)\n", "        \n", "        features_list.append(pd.DataFrame(sensor_features))\n", "    \n", "    return pd.concat(features_list, axis=1)\n", "\n", "# 3. Feature Statistics Function\n", "def compute_feature_statistics(X, y, save_path=\"img/feature_stats.txt\"):\n", "    print(\"\\nComputing feature statistics...\")\n", "    \n", "    nan_count = X.isna().sum().sum()\n", "    inf_count = np.isinf(X).sum().sum()\n", "    print(f\"NaN values in feature matrix: {nan_count}\")\n", "    print(f\"Infinite values in feature matrix: {inf_count}\")\n", "    \n", "    X_clean = <PERSON>.fillna(X.median()).replace([np.inf, -np.inf], [X.max(), X.min()])\n", "    \n", "    healthy_count = sum(y == 1)\n", "    unhealthy_count = sum(y == 0)\n", "    print(f\"Class distribution: Healthy (1): {healthy_count}, Unhealthy (0): {unhealthy_count}\")\n", "    \n", "    stats = X_clean.describe().T[['mean', 'std', 'min', 'max']]\n", "    stats['variance'] = stats['std'] ** 2\n", "    \n", "    healthy_stats = X_clean[y == 1].describe().T[['mean', 'std']]\n", "    unhealthy_stats = X_clean[y == 0].describe().T[['mean', 'std']]\n", "    stats = stats.join(healthy_stats.rename(columns={'mean': 'healthy_mean', 'std': 'healthy_std'}))\n", "    stats = stats.join(unhealthy_stats.rename(columns={'mean': 'unhealthy_mean', 'std': 'unhealthy_std'}))\n", "    \n", "    os.makedirs(os.path.dirname(save_path), exist_ok=True)\n", "    with open(save_path, 'w') as f:\n", "        f.write(\"Feature Statistics\\n\")\n", "        f.write(f\"NaN values: {nan_count}\\n\")\n", "        f.write(f\"Infinite values: {inf_count}\\n\")\n", "        f.write(f\"Class distribution: Healthy: {healthy_count}, Unhealthy: {unhealthy_count}\\n\\n\")\n", "        f.write(stats.to_string())\n", "    \n", "    print(f\"Feature statistics saved to {save_path}\")\n", "    return stats\n", "\n", "# 4. Visualization Functions\n", "def plot_acceleration_time(healthy_data, unhealthy_data, sampling_freq=1651, n_samples=3):\n", "    healthy_sample = healthy_data[:n_samples]\n", "    unhealthy_sample = unhealthy_data[:n_samples]\n", "    max_samples = int(25 * sampling_freq)\n", "    \n", "    for i, df in enumerate(healthy_sample):\n", "        n_samples = min(len(df), max_samples)\n", "        time = np.arange(0, n_samples/sampling_freq, 1/sampling_freq)\n", "        plt.figure(figsize=(3.5, 3))\n", "        df_plot = df.iloc[:n_samples]\n", "        for sensor in range(6):\n", "            plt.plot(time, df_plot.iloc[:, sensor], label=f'Sensor {sensor}', alpha=0.7)\n", "        plt.title(f'<PERSON><PERSON> Sample {i+1}', fontsize=10)\n", "        plt.xlabel('Time (s)', fontsize=8)\n", "        plt.ylabel('Acceleration', fontsize=8)\n", "        plt.legend(fontsize=7)\n", "        plt.grid(True)\n", "        plt.tick_params(axis='both', labelsize=7)\n", "        plt.tight_layout()\n", "        plt.savefig(f\"img/healthy_time_{i+1}.png\", dpi=300, bbox_inches='tight')\n", "        plt.close()\n", "    \n", "    for i, df in enumerate(unhealthy_sample):\n", "        n_samples = min(len(df), max_samples)\n", "        time = np.arange(0, n_samples/sampling_freq, 1/sampling_freq)\n", "        plt.figure(figsize=(3.5, 3))\n", "        df_plot = df.iloc[:n_samples]\n", "        for sensor in range(6):\n", "            plt.plot(time, df_plot.iloc[:, sensor], label=f'Sensor {sensor}', alpha=0.7)\n", "        plt.title(f'Unhealthy Sample {i+1}', fontsize=10)\n", "        plt.xlabel('Time (s)', fontsize=8)\n", "        plt.ylabel('Acceleration', fontsize=8)\n", "        plt.legend(fontsize=7)\n", "        plt.grid(True)\n", "        plt.tick_params(axis='both', labelsize=7)\n", "        plt.tight_layout()\n", "        plt.savefig(f\"img/unhealthy_time_{i+1}.png\", dpi=300, bbox_inches='tight')\n", "        plt.close()\n", "\n", "def plot_feature_distribution(X, y):\n", "    features_df = pd.DataFrame({\n", "        \"RMS\": X[\"sensor_0_rms\"],\n", "        \"Kurtosis\": X[\"sensor_0_kurtosis\"],\n", "        \"Spectral_Entropy\": X[\"sensor_0_spectral_entropy\"],\n", "        \"Class\": [\"Healthy\" if label == 1 else \"Unhealthy\" for label in y]\n", "    })\n", "    \n", "    plt.figure(figsize=(3.5, 3))\n", "    sns.violinplot(data=features_df.melt(id_vars=\"Class\"), x=\"variable\", y=\"value\", hue=\"Class\", split=True)\n", "    plt.xlabel(\"Feature\", fontsize=8)\n", "    plt.ylabel(\"Feature Value\", fontsize=8)\n", "    plt.title(\"Feature Distributions\", fontsize=10)\n", "    plt.tick_params(axis='both', labelsize=7)\n", "    plt.legend(title=\"Class\", fontsize=7)\n", "    plt.tight_layout()\n", "    plt.savefig(\"img/feature_distribution.png\", dpi=300, bbox_inches='tight')\n", "    plt.close()\n", "\n", "def plot_feature_trends(healthy_data, unhealthy_data):\n", "    if not healthy_data or not unhealthy_data:\n", "        print(\"Warning: No data available for feature trends plot.\")\n", "        return\n", "    \n", "    healthy_features = extract_features(healthy_data[0])\n", "    unhealthy_features = extract_features(unhealthy_data[0])\n", "    \n", "    if healthy_features.empty or unhealthy_features.empty:\n", "        print(\"Warning: Feature extraction failed for trends plot.\")\n", "        return\n", "    \n", "    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(3.5, 3), sharex=True)\n", "    windows = range(len(healthy_features))\n", "    ax1.plot(windows, healthy_features[\"sensor_0_rms\"], label=\"Healthy\")\n", "    ax1.plot(windows, unhealthy_features[\"sensor_0_rms\"], label=\"Unhealthy\")\n", "    ax2.plot(windows, healthy_features[\"sensor_0_spectral_power\"], label=\"Healthy\")\n", "    ax2.plot(windows, unhealthy_features[\"sensor_0_spectral_power\"], label=\"Unhealthy\")\n", "    ax1.set_title(\"RMS Over Windows\", fontsize=10)\n", "    ax2.set_title(\"Spectral Power Over Windows\", fontsize=10)\n", "    ax1.set_ylabel(\"RMS\", fontsize=8)\n", "    ax2.set_ylabel(\"Spectral Power\", fontsize=8)\n", "    ax2.set_xlabel(\"Window Index\", fontsize=8)\n", "    ax1.tick_params(axis='both', labelsize=7)\n", "    ax2.tick_params(axis='both', labelsize=7)\n", "    ax1.legend(fontsize=7)\n", "    ax2.legend(fontsize=7)\n", "    plt.tight_layout()\n", "    plt.savefig(\"img/feature_trends.png\", dpi=300, bbox_inches='tight')\n", "    plt.close()\n", "\n", "# 5. Metrics Table Function\n", "def print_metrics_table(y_test, y_pred, save_path=\"img/metrics_table.csv\"):\n", "    \"\"\"\n", "    Print and save a table of accuracy, precision, recall, and F1-score for each class.\n", "    \"\"\"\n", "    # Get classification report as dict\n", "    report = classification_report(y_test, y_pred, target_names=['Unhealthy', 'Healthy'], output_dict=True)\n", "    \n", "    # Extract metrics\n", "    metrics_data = {\n", "        'Class': ['Unhealthy', 'Healthy'],\n", "        'Precision': [report['Unhealthy']['precision'], report['Healthy']['precision']],\n", "        'Recall': [report['Unhealthy']['recall'], report['Healthy']['recall']],\n", "        'F1-Score': [report['Unhealthy']['f1-score'], report['Healthy']['f1-score']],\n", "        'Support': [report['Unhealthy']['support'], report['Healthy']['support']]\n", "    }\n", "    \n", "    # Add accuracy\n", "    accuracy = report['accuracy']\n", "    metrics_data['Class'].append('Overall')\n", "    metrics_data['Precision'].append(accuracy)\n", "    metrics_data['Recall'].append(np.nan)  # Accuracy doesn't apply to recall\n", "    metrics_data['F1-Score'].append(np.nan)  # Accuracy doesn't apply to F1\n", "    metrics_data['Support'].append(np.nan)  # No support for overall accuracy\n", "    \n", "    # Create DataFrame\n", "    metrics_df = pd.DataFrame(metrics_data)\n", "    \n", "    # Format for display (round to 3 decimal places)\n", "    metrics_df_display = metrics_df.copy()\n", "    metrics_df_display[['Precision', 'Recall', 'F1-Score']] = metrics_df_display[['Precision', 'Recall', 'F1-Score']].round(3)\n", "    \n", "    # Print table using tabulate\n", "    print(\"\\nClassification Metrics Table:\")\n", "    print(tabulate(metrics_df_display, headers='keys', tablefmt='grid', showindex=False))\n", "    \n", "    # Save to CSV\n", "    os.makedirs(os.path.dirname(save_path), exist_ok=True)\n", "    metrics_df.to_csv(save_path, index=False)\n", "    print(f\"Metrics table saved to {save_path}\")\n", "    \n", "    return metrics_df\n", "\n", "# 6. Main Processing Pipeline\n", "def main():\n", "    base_path = \"/Users/<USER>/Documents/bridge_sensor/\"\n", "    os.makedirs(\"img\", exist_ok=True)\n", "    \n", "    # Load data\n", "    healthy_data = load_sensor_data(base_path, \"healthy\")\n", "    unhealthy_data = load_sensor_data(base_path, \"unhealthy\")\n", "    \n", "    if not healthy_data and not unhealthy_data:\n", "        print(\"Error: No data loaded from either healthy or unhealthy folders.\")\n", "        return\n", "    \n", "    # Visualize time-series\n", "    print(\"\\nPlotting acceleration-time graphs...\")\n", "    plot_acceleration_time(healthy_data, unhealthy_data, n_samples=3)\n", "    \n", "    # Extract features\n", "    print(\"\\nExtracting features...\")\n", "    X_healthy = []\n", "    y_healthy = []\n", "    for df in healthy_data:\n", "        features = extract_features(df)\n", "        if not features.empty:\n", "            X_healthy.append(features)\n", "            y_healthy.extend([1] * len(features))\n", "    \n", "    X_unhealthy = []\n", "    y_unhealthy = []\n", "    for df in unhealthy_data:\n", "        features = extract_features(df)\n", "        if not features.empty:\n", "            X_unhealthy.append(features)\n", "            y_unhealthy.extend([0] * len(features))\n", "    \n", "    if not X_healthy and not X_unhealthy:\n", "        print(\"Error: No valid data extracted from any files.\")\n", "        return\n", "    \n", "    X = pd.concat(X_healthy + X_unhealthy, axis=0)\n", "    y = np.array(y_healthy + y_unhealthy)\n", "    \n", "    print(f\"\\nFeature matrix shape: {X.shape}\")\n", "    print(f\"Labels shape: {y.shape}\")\n", "    \n", "    # Compute feature statistics\n", "    stats = compute_feature_statistics(X, y)\n", "    \n", "    # Plot feature distribution and trends\n", "    plot_feature_distribution(X, y)\n", "    plot_feature_trends(healthy_data, unhealthy_data)\n", "    \n", "    # Split data\n", "    X_train, X_test, y_train, y_test = train_test_split(\n", "        X, y, test_size=0.2, random_state=42, stratify=y\n", "    )\n", "    \n", "    # Scale features\n", "    scaler = StandardScaler()\n", "    X_train_scaled = scaler.fit_transform(X_train)\n", "    X_test_scaled = scaler.transform(X_test)\n", "    \n", "    # XGBoost with RandomizedSearchCV\n", "    print(\"\\nPerforming RandomizedSearchCV with XGBoost...\")\n", "    xgb = XGBClassifier(\n", "        random_state=42,\n", "        eval_metric='logloss',\n", "        scale_pos_weight=sum(y==0)/sum(y==1)\n", "    )\n", "    \n", "    param_dist = {\n", "        'max_depth': [3, 5, 7, 9],\n", "        'learning_rate': [0.05, 0.1, 0.15, 0.2, 0.3],\n", "        'n_estimators': [100, 200, 300, 400, 500],\n", "        'subsample': [0.6, 0.7, 0.8, 0.9, 1.0],\n", "        'colsample_bytree': [0.6, 0.7, 0.8, 0.9, 1.0],\n", "        'gamma': [0, 0.1, 0.2, 0.3],\n", "        'min_child_weight': [1, 3, 5]\n", "    }\n", "    \n", "    random_search = RandomizedSearchCV(\n", "        estimator=xgb,\n", "        param_distributions=param_dist,\n", "        n_iter=50,\n", "        cv=5,\n", "        scoring='accuracy',\n", "        n_jobs=-1,\n", "        verbose=1,\n", "        random_state=42\n", "    )\n", "    \n", "    try:\n", "        random_search.fit(X_train_scaled, y_train)\n", "    except Exception as e:\n", "        print(f\"Error during RandomizedSearchCV fit: {str(e)}\")\n", "        print(\"Try updating xgboost (`pip install --upgrade xgboost`) or downgrading scikit-learn (`pip install scikit-learn==1.4.2`)\")\n", "        return\n", "    \n", "    print(\"\\nRandomizedSearchCV Results:\")\n", "    print(\"Best parameters:\", random_search.best_params_)\n", "    print(\"Best cross-validation score:\", random_search.best_score_)\n", "    \n", "    # Evaluate the best model\n", "    best_model = random_search.best_estimator_\n", "    y_pred = best_model.predict(X_test_scaled)\n", "    \n", "    # Print and save metrics table\n", "    metrics_df = print_metrics_table(y_test, y_pred)\n", "    \n", "    # Plot confusion matrix\n", "    plt.figure(figsize=(3.5, 3))\n", "    cm = confusion_matrix(y_test, y_pred)\n", "    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \n", "                xticklabels=['Unhealthy', 'Healthy'], \n", "                yticklabels=['Unhealthy', 'Healthy'])\n", "    plt.title('Confusion Matrix', fontsize=10)\n", "    plt.ylabel('True Label', fontsize=8)\n", "    plt.xlabel('Predicted Label', fontsize=8)\n", "    plt.tick_params(axis='both', labelsize=7)\n", "    plt.tight_layout()\n", "    plt.savefig(\"img/confusion_matrix.png\", dpi=300, bbox_inches='tight')\n", "    plt.close()\n", "    \n", "    # Plot ROC curve\n", "    y_pred_proba = best_model.predict_proba(X_test_scaled)[:, 1]\n", "    fpr, tpr, _ = roc_curve(y_test, y_pred_proba)\n", "    roc_auc = auc(fpr, tpr)\n", "    \n", "    plt.figure(figsize=(3.5, 3))\n", "    plt.plot(fpr, tpr, color='darkorange', lw=1, \n", "             label=f'ROC curve (AUC = {roc_auc:.2f})')\n", "    plt.plot([0, 1], [0, 1], color='navy', lw=1, linestyle='--')\n", "    plt.xlim([0.0, 1.0])\n", "    plt.ylim([0.0, 1.05])\n", "    plt.xlabel('False Positive Rate', fontsize=8)\n", "    plt.ylabel('True Positive Rate', fontsize=8)\n", "    plt.title('ROC Curve', fontsize=10)\n", "    plt.legend(loc=\"lower right\", fontsize=7)\n", "    plt.tick_params(axis='both', labelsize=7)\n", "    plt.tight_layout()\n", "    plt.savefig(\"img/roc_curve.png\", dpi=300, bbox_inches='tight')\n", "    plt.close()\n", "    \n", "    # Plot feature importance\n", "    plt.figure(figsize=(3.5, 3))\n", "    feat_importance = pd.DataFrame({\n", "        'feature': X.columns,\n", "        'importance': best_model.feature_importances_\n", "    })\n", "    feat_importance = feat_importance.sort_values('importance', ascending=False)\n", "    sns.barplot(x='importance', y='feature', data=feat_importance.head(10))\n", "    plt.title('Top 10 Feature Importances', fontsize=10)\n", "    plt.xlabel('Importance Score', fontsize=8)\n", "    plt.ylabel('Feature', fontsize=8)\n", "    plt.tick_params(axis='both', labelsize=7)\n", "    plt.tight_layout()\n", "    plt.savefig(\"img/feature_importance.png\", dpi=300, bbox_inches='tight')\n", "    plt.close()\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": 4, "id": "48001d9a", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'best_model' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[4], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m y_pred \u001b[38;5;241m=\u001b[39m \u001b[43mbest_model\u001b[49m\u001b[38;5;241m.\u001b[39mpredict(X_test_scaled)\n\u001b[1;32m      3\u001b[0m     \u001b[38;5;66;03m# Print and save metrics table\u001b[39;00m\n\u001b[1;32m      4\u001b[0m metrics_df \u001b[38;5;241m=\u001b[39m print_metrics_table(y_test, y_pred)\n", "\u001b[0;31mNameError\u001b[0m: name 'best_model' is not defined"]}], "source": ["y_pred = best_model.predict(X_test_scaled)\n", "    \n", "    # Print and save metrics table\n", "metrics_df = print_metrics_table(y_test, y_pred)"]}, {"cell_type": "code", "execution_count": null, "id": "5fdb3bc8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "dl-env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 5}