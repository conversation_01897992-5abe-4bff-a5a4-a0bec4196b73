import os
import pandas as pd
import numpy as np
from scipy.fft import fft
from scipy.stats import kurtosis, skew, entropy
from sklearn.model_selection import train_test_split, RandomizedSearchCV
from sklearn.preprocessing import StandardScaler
from xgboost import XGBClassifier
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc
import sklearn
import xgboost
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore", category=FutureWarning)

# Print library versions for debugging
print(f"scikit-learn version: {sklearn.__version__}")
print(f"xgboost version: {xgboost.__version__}")

# Set random seed for reproducibility
np.random.seed(42)

# 1. Data Loading Function
def load_sensor_data(base_path, condition):
    data_list = []
    folder_path = os.path.join(base_path, condition)
    print(f"\nLoading {condition} data from {folder_path}...")
    
    if not os.path.exists(folder_path):
        print(f"Error: Directory {folder_path} does not exist.")
        return data_list
    
    for root, _, files in os.walk(folder_path):
        for file in files:
            if file.endswith('.xlsx'):
                file_path = os.path.join(root, file)
                try:
                    df = pd.read_excel(file_path, usecols=range(6))
                    print(f"Loaded {file}: Shape {df.shape}")
                    data_list.append(df)
                except Exception as e:
                    print(f"Error loading {file}: {str(e)}")
    
    return data_list

# 2. Advanced Feature Extraction Function
def extract_features(df, sampling_freq=1651, start_sec=2, duration_sec=20, window_sec=0.5):
    start_idx = start_sec * sampling_freq
    end_idx = start_idx + (duration_sec * sampling_freq)
    
    if start_idx >= len(df):
        print(f"Warning: Start index exceeds data length for file with {len(df)} samples.")
        return pd.DataFrame()
    if end_idx > len(df):
        print(f"Warning: Data truncated from {len(df)} to {end_idx} samples.")
        end_idx = len(df)
    
    df_chunk = df.iloc[start_idx:end_idx]
    n_samples = len(df_chunk)
    window_size = int(window_sec * sampling_freq)
    n_windows = n_samples // window_size
    
    if n_windows == 0:
        print(f"Warning: No complete windows available in chunk of {n_samples} samples.")
        return pd.DataFrame()
    
    features_list = []
    for sensor in range(6):
        sensor_data = df_chunk.iloc[:, sensor]
        sensor_features = []
        
        for i in range(n_windows):
            chunk = sensor_data[i*window_size:(i+1)*window_size]
            
            # Time-domain features
            rms = np.sqrt(np.mean(chunk**2))
            peak_to_peak = np.max(chunk) - np.min(chunk)
            zero_crossings = np.sum(np.diff(np.sign(chunk)) != 0) / 2
            kurt = kurtosis(chunk, nan_policy='omit')
            crest_factor = np.max(np.abs(chunk)) / rms if rms != 0 else 0
            mad = np.mean(np.abs(chunk - np.mean(chunk)))
            skw = skew(chunk, nan_policy='omit')
            energy = np.sum(chunk**2)
            
            # Frequency-domain features
            fft_vals = fft(chunk.to_numpy())
            fft_magnitude = np.abs(fft_vals)[:window_size//2]
            freqs = np.fft.fftfreq(window_size, 1/sampling_freq)[:window_size//2]
            dominant_freq = freqs[np.argmax(fft_magnitude)] if fft_magnitude.size > 0 else 0
            spectral_power = np.sum(fft_magnitude**2) / window_size if fft_magnitude.size > 0 else 0
            fft_magnitude_sorted = np.sort(fft_magnitude)[::-1]
            second_dominant_freq = freqs[np.argsort(fft_magnitude)[-2]] if len(freqs) > 1 else 0
            spectral_entropy = entropy(fft_magnitude / np.sum(fft_magnitude)) if np.sum(fft_magnitude) != 0 else 0
            spectral_centroid = np.sum(freqs * fft_magnitude) / np.sum(fft_magnitude) if np.sum(fft_magnitude) != 0 else 0
            
            feat = {
                f'sensor_{sensor}_rms': rms,
                f'sensor_{sensor}_peak_to_peak': peak_to_peak,
                f'sensor_{sensor}_zero_crossings': zero_crossings,
                f'sensor_{sensor}_kurtosis': kurt,
                f'sensor_{sensor}_crest_factor': crest_factor,
                f'sensor_{sensor}_mad': mad,
                f'sensor_{sensor}_skewness': skw,
                f'sensor_{sensor}_energy': energy,
                f'sensor_{sensor}_dominant_freq': dominant_freq,
                f'sensor_{sensor}_second_dominant_freq': second_dominant_freq,
                f'sensor_{sensor}_spectral_power': spectral_power,
                f'sensor_{sensor}_spectral_entropy': spectral_entropy,
                f'sensor_{sensor}_spectral_centroid': spectral_centroid
            }
            sensor_features.append(feat)
        
        features_list.append(pd.DataFrame(sensor_features))
    
    return pd.concat(features_list, axis=1)

# 3. Feature Statistics Function
def compute_feature_statistics(X, y, save_path="img/feature_stats.txt"):
    print("\nComputing feature statistics...")
    
    # Check for missing or invalid values
    nan_count = X.isna().sum().sum()
    inf_count = np.isinf(X).sum().sum()
    print(f"NaN values in feature matrix: {nan_count}")
    print(f"Infinite values in feature matrix: {inf_count}")
    
    # Replace NaNs with median and infinities with max/min
    X_clean = X.fillna(X.median()).replace([np.inf, -np.inf], [X.max(), X.min()])
    
    # Class distribution
    healthy_count = sum(y == 1)
    unhealthy_count = sum(y == 0)
    print(f"Class distribution: Healthy (1): {healthy_count}, Unhealthy (0): {unhealthy_count}")
    
    # Summary statistics
    stats = X_clean.describe().T[['mean', 'std', 'min', 'max']]
    stats['variance'] = stats['std'] ** 2
    
    # Group by class
    healthy_stats = X_clean[y == 1].describe().T[['mean', 'std']]
    unhealthy_stats = X_clean[y == 0].describe().T[['mean', 'std']]
    stats = stats.join(healthy_stats.rename(columns={'mean': 'healthy_mean', 'std': 'healthy_std'}))
    stats = stats.join(unhealthy_stats.rename(columns={'mean': 'unhealthy_mean', 'std': 'unhealthy_std'}))
    
    # Save to file
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    with open(save_path, 'w') as f:
        f.write("Feature Statistics\n")
        f.write(f"NaN values: {nan_count}\n")
        f.write(f"Infinite values: {inf_count}\n")
        f.write(f"Class distribution: Healthy: {healthy_count}, Unhealthy: {unhealthy_count}\n\n")
        f.write(stats.to_string())
    
    print(f"Feature statistics saved to {save_path}")
    return stats

# 4. Visualization Functions
def plot_acceleration_time(healthy_data, unhealthy_data, sampling_freq=1651, n_samples=3):
    healthy_sample = healthy_data[:n_samples]
    unhealthy_sample = unhealthy_data[:n_samples]
    max_samples = int(25 * sampling_freq)
    
    for i, df in enumerate(healthy_sample):
        n_samples = min(len(df), max_samples)
        time = np.arange(0, n_samples/sampling_freq, 1/sampling_freq)
        plt.figure(figsize=(3.5, 3))
        df_plot = df.iloc[:n_samples]
        for sensor in range(6):
            plt.plot(time, df_plot.iloc[:, sensor], label=f'Sensor {sensor}', alpha=0.7)
        plt.title(f'Healthy Sample {i+1}', fontsize=10)
        plt.xlabel('Time (s)', fontsize=8)
        plt.ylabel('Acceleration', fontsize=8)
        plt.legend(fontsize=7)
        plt.grid(True)
        plt.tick_params(axis='both', labelsize=7)
        plt.tight_layout()
        plt.savefig(f"img/healthy_time_{i+1}.png", dpi=300, bbox_inches='tight')
        plt.close()
    
    for i, df in enumerate(unhealthy_sample):
        n_samples = min(len(df), max_samples)
        time = np.arange(0, n_samples/sampling_freq, 1/sampling_freq)
        plt.figure(figsize=(3.5, 3))
        df_plot = df.iloc[:n_samples]
        for sensor in range(6):
            plt.plot(time, df_plot.iloc[:, sensor], label=f'Sensor {sensor}', alpha=0.7)
        plt.title(f'Unhealthy Sample {i+1}', fontsize=10)
        plt.xlabel('Time (s)', fontsize=8)
        plt.ylabel('Acceleration', fontsize=8)
        plt.legend(fontsize=7)
        plt.grid(True)
        plt.tick_params(axis='both', labelsize=7)
        plt.tight_layout()
        plt.savefig(f"img/unhealthy_time_{i+1}.png", dpi=300, bbox_inches='tight')
        plt.close()

def plot_feature_distribution(X, y):
    features_df = pd.DataFrame({
        "RMS": X["sensor_0_rms"],
        "Kurtosis": X["sensor_0_kurtosis"],
        "Spectral_Entropy": X["sensor_0_spectral_entropy"],
        "Class": ["Healthy" if label == 1 else "Unhealthy" for label in y]
    })
    
    plt.figure(figsize=(3.5, 3))
    sns.violinplot(data=features_df.melt(id_vars="Class"), x="variable", y="value", hue="Class", split=True)
    plt.xlabel("Feature", fontsize=8)
    plt.ylabel("Feature Value", fontsize=8)
    plt.title("Feature Distributions", fontsize=10)
    plt.tick_params(axis='both', labelsize=7)
    plt.legend(title="Class", fontsize=7)
    plt.tight_layout()
    plt.savefig("img/feature_distribution.png", dpi=300, bbox_inches='tight')
    plt.close()

def plot_feature_trends(healthy_data, unhealthy_data):
    if not healthy_data or not unhealthy_data:
        print("Warning: No data available for feature trends plot.")
        return
    
    healthy_features = extract_features(healthy_data[0])
    unhealthy_features = extract_features(unhealthy_data[0])
    
    if healthy_features.empty or unhealthy_features.empty:
        print("Warning: Feature extraction failed for trends plot.")
        return
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(3.5, 3), sharex=True)
    windows = range(len(healthy_features))
    ax1.plot(windows, healthy_features["sensor_0_rms"], label="Healthy")
    ax1.plot(windows, unhealthy_features["sensor_0_rms"], label="Unhealthy")
    ax2.plot(windows, healthy_features["sensor_0_spectral_power"], label="Healthy")
    ax2.plot(windows, unhealthy_features["sensor_0_spectral_power"], label="Unhealthy")
    ax1.set_title("RMS Over Windows", fontsize=10)
    ax2.set_title("Spectral Power Over Windows", fontsize=10)
    ax1.set_ylabel("RMS", fontsize=8)
    ax2.set_ylabel("Spectral Power", fontsize=8)
    ax2.set_xlabel("Window Index", fontsize=8)
    ax1.tick_params(axis='both', labelsize=7)
    ax2.tick_params(axis='both', labelsize=7)
    ax1.legend(fontsize=7)
    ax2.legend(fontsize=7)
    plt.tight_layout()
    plt.savefig("img/feature_trends.png", dpi=300, bbox_inches='tight')
    plt.close()

# 5. Main Processing Pipeline
def main():
    base_path = "/Users/<USER>/Documents/bridge_sensor/"
    os.makedirs("img", exist_ok=True)
    
    # Load data
    healthy_data = load_sensor_data(base_path, "healthy")
    unhealthy_data = load_sensor_data(base_path, "unhealthy")
    
    if not healthy_data and not unhealthy_data:
        print("Error: No data loaded from either healthy or unhealthy folders.")
        return
    
    # Visualize time-series
    print("\nPlotting acceleration-time graphs...")
    plot_acceleration_time(healthy_data, unhealthy_data, n_samples=3)
    
    # Extract features
    print("\nExtracting features...")
    X_healthy = []
    y_healthy = []
    for df in healthy_data:
        features = extract_features(df)
        if not features.empty:
            X_healthy.append(features)
            y_healthy.extend([1] * len(features))
    
    X_unhealthy = []
    y_unhealthy = []
    for df in unhealthy_data:
        features = extract_features(df)
        if not features.empty:
            X_unhealthy.append(features)
            y_unhealthy.extend([0] * len(features))
    
    if not X_healthy and not X_unhealthy:
        print("Error: No valid data extracted from any files.")
        return
    
    X = pd.concat(X_healthy + X_unhealthy, axis=0)
    y = np.array(y_healthy + y_unhealthy)
    
    print(f"\nFeature matrix shape: {X.shape}")
    print(f"Labels shape: {y.shape}")
    
    # Compute feature statistics
    stats = compute_feature_statistics(X, y)
    
    # Plot feature distribution and trends
    plot_feature_distribution(X, y)
    plot_feature_trends(healthy_data, unhealthy_data)
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # Scale features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # XGBoost with RandomizedSearchCV
    print("\nPerforming RandomizedSearchCV with XGBoost...")
    xgb = XGBClassifier(
        random_state=42,
        eval_metric='logloss',
        scale_pos_weight=sum(y==0)/sum(y==1)
    )
    
    param_dist = {
        'max_depth': [3, 5, 7, 9],
        'learning_rate': [0.05, 0.1, 0.15, 0.2, 0.3],
        'n_estimators': [100, 200, 300, 400, 500],
        'subsample': [0.6, 0.7, 0.8, 0.9, 1.0],
        'colsample_bytree': [0.6, 0.7, 0.8, 0.9, 1.0],
        'gamma': [0, 0.1, 0.2, 0.3],
        'min_child_weight': [1, 3, 5]
    }
    
    random_search = RandomizedSearchCV(
        estimator=xgb,
        param_distributions=param_dist,
        n_iter=50,
        cv=5,
        scoring='accuracy',
        n_jobs=-1,
        verbose=1,
        random_state=42
    )
    
    try:
        random_search.fit(X_train_scaled, y_train)
    except Exception as e:
        print(f"Error during RandomizedSearchCV fit: {str(e)}")
        print("Try updating xgboost (`pip install --upgrade xgboost`) or downgrading scikit-learn (`pip install scikit-learn==1.4.2`)")
        return
    
    print("\nRandomizedSearchCV Results:")
    print("Best parameters:", random_search.best_params_)
    print("Best cross-validation score:", random_search.best_score_)
    
    # Evaluate the best model
    best_model = random_search.best_estimator_
    y_pred = best_model.predict(X_test_scaled)
    
    print("\nClassification Report:")
    print(classification_report(y_test, y_pred, target_names=['Unhealthy (0)', 'Healthy (1)']))
    
    # Plot confusion matrix
    plt.figure(figsize=(3.5, 3))
    cm = confusion_matrix(y_test, y_pred)
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=['Unhealthy', 'Healthy'], 
                yticklabels=['Unhealthy', 'Healthy'])
    plt.title('Confusion Matrix', fontsize=10)
    plt.ylabel('True Label', fontsize=8)
    plt.xlabel('Predicted Label', fontsize=8)
    plt.tick_params(axis='both', labelsize=7)
    plt.tight_layout()
    plt.savefig("img/confusion_matrix.png", dpi=300, bbox_inches='tight')
    plt.close()
    
    # Plot ROC curve
    y_pred_proba = best_model.predict_proba(X_test_scaled)[:, 1]
    fpr, tpr, _ = roc_curve(y_test, y_pred_proba)
    roc_auc = auc(fpr, tpr)
    
    plt.figure(figsize=(3.5, 3))
    plt.plot(fpr, tpr, color='darkorange', lw=1, 
             label=f'ROC curve (AUC = {roc_auc:.2f})')
    plt.plot([0, 1], [0, 1], color='navy', lw=1, linestyle='--')
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('False Positive Rate', fontsize=8)
    plt.ylabel('True Positive Rate', fontsize=8)
    plt.title('ROC Curve', fontsize=10)
    plt.legend(loc="lower right", fontsize=7)
    plt.tick_params(axis='both', labelsize=7)
    plt.tight_layout()
    plt.savefig("img/roc_curve.png", dpi=300, bbox_inches='tight')
    plt.close()
    
    # Plot feature importance
    plt.figure(figsize=(3.5, 3))
    feat_importance = pd.DataFrame({
        'feature': X.columns,
        'importance': best_model.feature_importances_
    })
    feat_importance = feat_importance.sort_values('importance', ascending=False)
    sns.barplot(x='importance', y='feature', data=feat_importance.head(10))
    plt.title('Top 10 Feature Importances', fontsize=10)
    plt.xlabel('Importance Score', fontsize=8)
    plt.ylabel('Feature', fontsize=8)
    plt.tick_params(axis='both', labelsize=7)
    plt.tight_layout()
    plt.savefig("img/feature_importance.png", dpi=300, bbox_inches='tight')
    plt.close()

if __name__ == "__main__":
    main()