{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Num GPUs Available:  1\n"]}], "source": ["import tensorflow as tf\n", "print(\"Num GPUs Available: \", len(tf.config.list_physical_devices('GPU')))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "ImportError", "evalue": "cannot import name 'regnet' from 'tensorflow.keras.applications' (/Users/<USER>/dl-env/lib/python3.10/site-packages/keras/_tf_keras/keras/applications/__init__.py)", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mImportError\u001b[0m                               Traceback (most recent call last)", "Cell \u001b[0;32mIn[3], line 7\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mlayers\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m <PERSON><PERSON>, GlobalAveragePooling1D, Input\n\u001b[1;32m      6\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmodels\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Model\n\u001b[0;32m----> 7\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mapplications\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m regnet\n\u001b[1;32m      8\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mscipy\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msignal\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m butter, filtfilt\n\u001b[1;32m      9\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01msklearn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpreprocessing\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m MinMaxScaler\n", "\u001b[0;31mImportError\u001b[0m: cannot import name 'regnet' from 'tensorflow.keras.applications' (/Users/<USER>/dl-env/lib/python3.10/site-packages/keras/_tf_keras/keras/applications/__init__.py)"]}], "source": ["import os\n", "import numpy as np\n", "import pandas as pd\n", "import tensorflow as tf\n", "from tensorflow.keras.layers import Dense, GlobalAveragePooling1D, Input\n", "from tensorflow.keras.models import Model\n", "from tensorflow.keras.applications import regnet\n", "from scipy.signal import butter, filtfilt\n", "from sklearn.preprocessing import MinMaxScaler\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import confusion_matrix, classification_report\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "# Define dataset paths\n", "healthy_path = 'healthy/traindata_201911'\n", "unhealthy_path = 'unhealthy/traindata_201903'\n", "\n", "# Butterworth filter function\n", "def butter_lowpass_filter(data, cutoff=10, fs=1651, order=4):\n", "    b, a = butter(order, cutoff/(0.5*fs), btype='low')\n", "    return filtfilt(b, a, data, axis=0)\n", "\n", "# Data loading & preprocessing function (each file as an instance)\n", "def load_and_preprocess(path, label):\n", "    instances, labels = [], []\n", "    files = [f for f in os.listdir(path) if f.endswith('.xlsx')]\n", "    print(f'Loading from {path}, total files: {len(files)}')\n", "    \n", "    for file in files:\n", "        df = pd.read_excel(os.path.join(path, file)).iloc[:, :6]\n", "        \n", "        # Handling missing values\n", "        df.interpolate(method='linear', inplace=True)\n", "        data = df.values\n", "        \n", "        # Noise removal using Butterworth filter\n", "        data = butter_lowpass_filter(data)\n", "\n", "        # Normalize data\n", "        scaler = MinMaxScaler()\n", "        data = scaler.fit_transform(data)\n", "\n", "        # Ensure fixed-length: pad or trim data to exactly 40 sec (40 * 1651 = 66040 samples)\n", "        target_length = 40 * 1651\n", "        current_length = data.shape[0]\n", "        \n", "        if current_length < target_length:\n", "            padding = np.zeros((target_length - current_length, 6))\n", "            data = np.vstack((data, padding))\n", "        else:\n", "            data = data[:target_length, :]\n", "\n", "        instances.append(data)\n", "        labels.append(label)\n", "\n", "    return np.array(instances), np.array(labels)\n", "\n", "# Load both healthy and unhealthy data\n", "healthy_data, healthy_labels = load_and_preprocess(healthy_path, label=0)\n", "unhealthy_data, unhealthy_labels = load_and_preprocess(unhealthy_path, label=1)\n", "\n", "# Combine dataset\n", "X = np.concatenate([healthy_data, unhealthy_data], axis=0)\n", "y = np.concatenate([healthy_labels, unhealthy_labels], axis=0)\n", "\n", "print(f'Total dataset shape: {X.shape}, Labels shape: {y.shape}')\n", "\n", "# Train-test split\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, random_state=42, shuffle=True\n", ")\n", "\n", "# Define RegNetY model adapted for 1D signals\n", "input_shape = (X_train.shape[1], X_train.shape[2])\n", "input_tensor = Input(shape=input_shape)\n", "\n", "# Expand dimensions to match RegNet requirement (RegNet is for 2D images, so we simulate channels)\n", "x_expanded = tf.expand_dims(input_tensor, axis=-1)  # shape: (batch, length, channels=6, 1)\n", "\n", "# RegNet Model (using RegNetY004 as a base)\n", "base_model = regnet.RegNetY004(\n", "    include_top=False, weights=None, input_shape=(input_shape[0], input_shape[1], 1)\n", ")\n", "\n", "x = base_model(x_expanded, training=True)\n", "x = GlobalAveragePooling1D()(tf.squeeze(x, axis=2))\n", "output = Dense(1, activation='sigmoid')(x)\n", "\n", "model = Model(inputs=input_tensor, outputs=output)\n", "\n", "# Compile the model\n", "model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])\n", "model.summary()\n", "\n", "# Train the model\n", "history = model.fit(\n", "    X_train, y_train, \n", "    epochs=20, batch_size=8, \n", "    validation_split=0.2, verbose=1\n", ")\n", "\n", "# Evaluate model\n", "loss, acc = model.evaluate(X_test, y_test, verbose=0)\n", "print(f\"Test Accuracy: {acc*100:.2f}%\")\n", "\n", "# Predictions and evaluation metrics\n", "y_pred = (model.predict(X_test) > 0.5).astype(int)\n", "\n", "# Confusion matrix visualization\n", "cm = confusion_matrix(y_test, y_pred)\n", "plt.figure(figsize=(6, 5))\n", "sns.heatmap(cm, annot=True, fmt=\"d\", cmap='Blues', xticklabels=['Healthy', 'Unhealthy'], yticklabels=['Healthy', 'Unhealthy'])\n", "plt.title('Confusion Matrix')\n", "plt.ylabel('Actual')\n", "plt.xlabel('Predicted')\n", "plt.show()\n", "\n", "# Classification Report\n", "print(\"Classification Report:\")\n", "print(classification_report(y_test, y_pred, target_names=['Healthy', 'Unhealthy']))"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: transformers in /Users/<USER>/dl-env/lib/python3.10/site-packages (4.47.1)\n", "Requirement already satisfied: filelock in /Users/<USER>/dl-env/lib/python3.10/site-packages (from transformers) (3.13.1)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.24.0 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from transformers) (0.27.0)\n", "Requirement already satisfied: numpy>=1.17 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from transformers) (1.26.2)\n", "Requirement already satisfied: packaging>=20.0 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from transformers) (23.2)\n", "Requirement already satisfied: pyyaml>=5.1 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from transformers) (6.0.2)\n", "Requirement already satisfied: regex!=2019.12.17 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from transformers) (2024.11.6)\n", "Requirement already satisfied: requests in /Users/<USER>/dl-env/lib/python3.10/site-packages (from transformers) (2.32.3)\n", "Requirement already satisfied: tokenizers<0.22,>=0.21 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from transformers) (0.21.0)\n", "Requirement already satisfied: safetensors>=0.4.1 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from transformers) (0.4.5)\n", "Requirement already satisfied: tqdm>=4.27 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from transformers) (4.67.1)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from huggingface-hub<1.0,>=0.24.0->transformers) (2024.2.0)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from huggingface-hub<1.0,>=0.24.0->transformers) (4.12.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from requests->transformers) (3.4.0)\n", "Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from requests->transformers) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from requests->transformers) (2.2.3)\n", "Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/dl-env/lib/python3.10/site-packages (from requests->transformers) (2024.8.30)\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m24.3.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install transformers"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading from healthy/traindata_201911, total files: 59\n", "Loading from unhealthy/traindata_201903, total files: 62\n", "Total dataset shape: (121, 66040, 6), Labels shape: (121,)\n"]}, {"ename": "OSError", "evalue": "facebook/regnet-y-400mf is not a local folder and is not a valid model identifier listed on 'https://huggingface.co/models'\nIf this is a private repository, make sure to pass a token having permission to this repo either by logging in with `huggingface-cli login` or by passing `token=<your_token>`", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mHTTPError\u001b[0m                                 Traceback (most recent call last)", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/huggingface_hub/utils/_http.py:406\u001b[0m, in \u001b[0;36mhf_raise_for_status\u001b[0;34m(response, endpoint_name)\u001b[0m\n\u001b[1;32m    405\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 406\u001b[0m     \u001b[43mresponse\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mraise_for_status\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    407\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m HTTPError \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/requests/models.py:1024\u001b[0m, in \u001b[0;36mResponse.raise_for_status\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1023\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m http_error_msg:\n\u001b[0;32m-> 1024\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m HTTPError(http_error_msg, response\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m)\n", "\u001b[0;31mHTTPError\u001b[0m: 401 Client Error: Unauthorized for url: https://huggingface.co/facebook/regnet-y-400mf/resolve/main/config.json", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[0;31mRepositoryNotFoundError\u001b[0m                   <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/transformers/utils/hub.py:403\u001b[0m, in \u001b[0;36mcached_file\u001b[0;34m(path_or_repo_id, filename, cache_dir, force_download, resume_download, proxies, token, revision, local_files_only, subfolder, repo_type, user_agent, _raise_exceptions_for_gated_repo, _raise_exceptions_for_missing_entries, _raise_exceptions_for_connection_errors, _commit_hash, **deprecated_kwargs)\u001b[0m\n\u001b[1;32m    401\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    402\u001b[0m     \u001b[38;5;66;03m# Load from URL or cache if already cached\u001b[39;00m\n\u001b[0;32m--> 403\u001b[0m     resolved_file \u001b[38;5;241m=\u001b[39m \u001b[43mhf_hub_download\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    404\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpath_or_repo_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    405\u001b[0m \u001b[43m        \u001b[49m\u001b[43mfilename\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    406\u001b[0m \u001b[43m        \u001b[49m\u001b[43msubfolder\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mlen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43msubfolder\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m==\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43msubfolder\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    407\u001b[0m \u001b[43m        \u001b[49m\u001b[43mrepo_type\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrepo_type\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    408\u001b[0m \u001b[43m        \u001b[49m\u001b[43mrevision\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrevision\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    409\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcache_dir\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcache_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    410\u001b[0m \u001b[43m        \u001b[49m\u001b[43muser_agent\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43muser_agent\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    411\u001b[0m \u001b[43m        \u001b[49m\u001b[43mforce_download\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mforce_download\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    412\u001b[0m \u001b[43m        \u001b[49m\u001b[43mproxies\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mproxies\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    413\u001b[0m \u001b[43m        \u001b[49m\u001b[43mresume_download\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mresume_download\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    414\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtoken\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtoken\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    415\u001b[0m \u001b[43m        \u001b[49m\u001b[43mlocal_files_only\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlocal_files_only\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    416\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    417\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m GatedRepoError \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/huggingface_hub/utils/_validators.py:114\u001b[0m, in \u001b[0;36mvalidate_hf_hub_args.<locals>._inner_fn\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    112\u001b[0m     kwargs \u001b[38;5;241m=\u001b[39m smoothly_deprecate_use_auth_token(fn_name\u001b[38;5;241m=\u001b[39mfn\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m, has_token\u001b[38;5;241m=\u001b[39mhas_token, kwargs\u001b[38;5;241m=\u001b[39mkwargs)\n\u001b[0;32m--> 114\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/huggingface_hub/file_download.py:860\u001b[0m, in \u001b[0;36mhf_hub_download\u001b[0;34m(repo_id, filename, subfolder, repo_type, revision, library_name, library_version, cache_dir, local_dir, user_agent, force_download, proxies, etag_timeout, token, local_files_only, headers, endpoint, resume_download, force_filename, local_dir_use_symlinks)\u001b[0m\n\u001b[1;32m    859\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 860\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_hf_hub_download_to_cache_dir\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    861\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;66;43;03m# Destination\u001b[39;49;00m\n\u001b[1;32m    862\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcache_dir\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcache_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    863\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;66;43;03m# File info\u001b[39;49;00m\n\u001b[1;32m    864\u001b[0m \u001b[43m        \u001b[49m\u001b[43mrepo_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrepo_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    865\u001b[0m \u001b[43m        \u001b[49m\u001b[43mfilename\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mfilename\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    866\u001b[0m \u001b[43m        \u001b[49m\u001b[43mrepo_type\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrepo_type\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    867\u001b[0m \u001b[43m        \u001b[49m\u001b[43mrevision\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrevision\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    868\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;66;43;03m# HTTP info\u001b[39;49;00m\n\u001b[1;32m    869\u001b[0m \u001b[43m        \u001b[49m\u001b[43mendpoint\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mendpoint\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    870\u001b[0m \u001b[43m        \u001b[49m\u001b[43metag_timeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43metag_timeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    871\u001b[0m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mhf_headers\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    872\u001b[0m \u001b[43m        \u001b[49m\u001b[43mproxies\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mproxies\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    873\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtoken\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtoken\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    874\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;66;43;03m# Additional options\u001b[39;49;00m\n\u001b[1;32m    875\u001b[0m \u001b[43m        \u001b[49m\u001b[43mlocal_files_only\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlocal_files_only\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    876\u001b[0m \u001b[43m        \u001b[49m\u001b[43mforce_download\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mforce_download\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    877\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/huggingface_hub/file_download.py:967\u001b[0m, in \u001b[0;36m_hf_hub_download_to_cache_dir\u001b[0;34m(cache_dir, repo_id, filename, repo_type, revision, endpoint, etag_timeout, headers, proxies, token, local_files_only, force_download)\u001b[0m\n\u001b[1;32m    966\u001b[0m     \u001b[38;5;66;03m# Otherwise, raise appropriate error\u001b[39;00m\n\u001b[0;32m--> 967\u001b[0m     \u001b[43m_raise_on_head_call_error\u001b[49m\u001b[43m(\u001b[49m\u001b[43mhead_call_error\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mforce_download\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlocal_files_only\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    969\u001b[0m \u001b[38;5;66;03m# From now on, etag, commit_hash, url and size are not None.\u001b[39;00m\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/huggingface_hub/file_download.py:1482\u001b[0m, in \u001b[0;36m_raise_on_head_call_error\u001b[0;34m(head_call_error, force_download, local_files_only)\u001b[0m\n\u001b[1;32m   1480\u001b[0m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(head_call_error, RepositoryNotFoundError) \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(head_call_error, GatedRepoError):\n\u001b[1;32m   1481\u001b[0m     \u001b[38;5;66;03m# Repo not found or gated => let's raise the actual error\u001b[39;00m\n\u001b[0;32m-> 1482\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m head_call_error\n\u001b[1;32m   1483\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1484\u001b[0m     \u001b[38;5;66;03m# Otherwise: most likely a connection issue or Hub downtime => let's warn the user\u001b[39;00m\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/huggingface_hub/file_download.py:1374\u001b[0m, in \u001b[0;36m_get_metadata_or_catch_error\u001b[0;34m(repo_id, filename, repo_type, revision, endpoint, proxies, etag_timeout, headers, token, local_files_only, relative_filename, storage_folder)\u001b[0m\n\u001b[1;32m   1373\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1374\u001b[0m     metadata \u001b[38;5;241m=\u001b[39m \u001b[43mget_hf_file_metadata\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1375\u001b[0m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mproxies\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mproxies\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43metag_timeout\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtoken\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtoken\u001b[49m\n\u001b[1;32m   1376\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1377\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m EntryNotFoundError \u001b[38;5;28;01mas\u001b[39;00m http_error:\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/huggingface_hub/utils/_validators.py:114\u001b[0m, in \u001b[0;36mvalidate_hf_hub_args.<locals>._inner_fn\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    112\u001b[0m     kwargs \u001b[38;5;241m=\u001b[39m smoothly_deprecate_use_auth_token(fn_name\u001b[38;5;241m=\u001b[39mfn\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m, has_token\u001b[38;5;241m=\u001b[39mhas_token, kwargs\u001b[38;5;241m=\u001b[39mkwargs)\n\u001b[0;32m--> 114\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/huggingface_hub/file_download.py:1294\u001b[0m, in \u001b[0;36mget_hf_file_metadata\u001b[0;34m(url, token, proxies, timeout, library_name, library_version, user_agent, headers)\u001b[0m\n\u001b[1;32m   1293\u001b[0m \u001b[38;5;66;03m# Retrieve metadata\u001b[39;00m\n\u001b[0;32m-> 1294\u001b[0m r \u001b[38;5;241m=\u001b[39m \u001b[43m_request_wrapper\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1295\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mHEAD\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1296\u001b[0m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1297\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mhf_headers\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1298\u001b[0m \u001b[43m    \u001b[49m\u001b[43mallow_redirects\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m   1299\u001b[0m \u001b[43m    \u001b[49m\u001b[43mfollow_relative_redirects\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m   1300\u001b[0m \u001b[43m    \u001b[49m\u001b[43mproxies\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mproxies\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1301\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1302\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1303\u001b[0m hf_raise_for_status(r)\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/huggingface_hub/file_download.py:278\u001b[0m, in \u001b[0;36m_request_wrapper\u001b[0;34m(method, url, follow_relative_redirects, **params)\u001b[0m\n\u001b[1;32m    277\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m follow_relative_redirects:\n\u001b[0;32m--> 278\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[43m_request_wrapper\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    279\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    280\u001b[0m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    281\u001b[0m \u001b[43m        \u001b[49m\u001b[43mfollow_relative_redirects\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01<PERSON><PERSON><PERSON><PERSON>\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    282\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mparams\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    283\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    285\u001b[0m     \u001b[38;5;66;03m# If redirection, we redirect only relative paths.\u001b[39;00m\n\u001b[1;32m    286\u001b[0m     \u001b[38;5;66;03m# This is useful in case of a renamed repository.\u001b[39;00m\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/huggingface_hub/file_download.py:302\u001b[0m, in \u001b[0;36m_request_wrapper\u001b[0;34m(method, url, follow_relative_redirects, **params)\u001b[0m\n\u001b[1;32m    301\u001b[0m response \u001b[38;5;241m=\u001b[39m get_session()\u001b[38;5;241m.\u001b[39mrequest(method\u001b[38;5;241m=\u001b[39mmethod, url\u001b[38;5;241m=\u001b[39murl, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mparams)\n\u001b[0;32m--> 302\u001b[0m \u001b[43mhf_raise_for_status\u001b[49m\u001b[43m(\u001b[49m\u001b[43mresponse\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    303\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m response\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/huggingface_hub/utils/_http.py:454\u001b[0m, in \u001b[0;36mhf_raise_for_status\u001b[0;34m(response, endpoint_name)\u001b[0m\n\u001b[1;32m    446\u001b[0m     message \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m    447\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mresponse\u001b[38;5;241m.\u001b[39mstatus_code\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m Client Error.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    448\u001b[0m         \u001b[38;5;241m+\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    452\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m make sure you are authenticated.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    453\u001b[0m     )\n\u001b[0;32m--> 454\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m _format(RepositoryNotFoundError, message, response) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01me\u001b[39;00m\n\u001b[1;32m    456\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m response\u001b[38;5;241m.\u001b[39mstatus_code \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m400\u001b[39m:\n", "\u001b[0;31mRepositoryNotFoundError\u001b[0m: 401 Client Error. (Request ID: Root=1-67e055e1-0b3dba4e67102b21781820fc;cda130c0-dfa9-4aa3-9add-0068e4defb58)\n\nRepository Not Found for url: https://huggingface.co/facebook/regnet-y-400mf/resolve/main/config.json.\nPlease make sure you specified the correct `repo_id` and `repo_type`.\nIf you are trying to access a private or gated repo, make sure you are authenticated.\nInvalid username or password.", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[0;31mOSError\u001b[0m                                   Traceback (most recent call last)", "Cell \u001b[0;32mIn[2], line 75\u001b[0m\n\u001b[1;32m     70\u001b[0m X_train, X_test, y_train, y_test \u001b[38;5;241m=\u001b[39m train_test_split(\n\u001b[1;32m     71\u001b[0m     X, y, test_size\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0.2\u001b[39m, random_state\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m42\u001b[39m, shuffle\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[1;32m     72\u001b[0m )\n\u001b[1;32m     74\u001b[0m \u001b[38;5;66;03m# Define RegNet model from Hugging Face\u001b[39;00m\n\u001b[0;32m---> 75\u001b[0m configuration \u001b[38;5;241m=\u001b[39m \u001b[43mRegNetConfig\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfrom_pretrained\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mfacebook/regnet-y-400mf\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m     76\u001b[0m model \u001b[38;5;241m=\u001b[39m RegNetModel\u001b[38;5;241m.\u001b[39mfrom_pretrained(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfacebook/regnet-y-400mf\u001b[39m\u001b[38;5;124m'\u001b[39m, config\u001b[38;5;241m=\u001b[39mconfiguration)\n\u001b[1;32m     78\u001b[0m \u001b[38;5;66;03m# Define input shape for the time-series data\u001b[39;00m\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/transformers/configuration_utils.py:550\u001b[0m, in \u001b[0;36mPretrainedConfig.from_pretrained\u001b[0;34m(cls, pretrained_model_name_or_path, cache_dir, force_download, local_files_only, token, revision, **kwargs)\u001b[0m\n\u001b[1;32m    546\u001b[0m kwargs[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrevision\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m revision\n\u001b[1;32m    548\u001b[0m \u001b[38;5;28mcls\u001b[39m\u001b[38;5;241m.\u001b[39m_set_token_in_kwargs(kwargs, token)\n\u001b[0;32m--> 550\u001b[0m config_dict, kwargs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mcls\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_config_dict\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpretrained_model_name_or_path\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    551\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mcls\u001b[39m\u001b[38;5;241m.\u001b[39mbase_config_key \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mcls\u001b[39m\u001b[38;5;241m.\u001b[39mbase_config_key \u001b[38;5;129;01min\u001b[39;00m config_dict:\n\u001b[1;32m    552\u001b[0m     config_dict \u001b[38;5;241m=\u001b[39m config_dict[\u001b[38;5;28mcls\u001b[39m\u001b[38;5;241m.\u001b[39mbase_config_key]\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/transformers/configuration_utils.py:590\u001b[0m, in \u001b[0;36mPretrainedConfig.get_config_dict\u001b[0;34m(cls, pretrained_model_name_or_path, **kwargs)\u001b[0m\n\u001b[1;32m    588\u001b[0m original_kwargs \u001b[38;5;241m=\u001b[39m copy\u001b[38;5;241m.\u001b[39mdeepcopy(kwargs)\n\u001b[1;32m    589\u001b[0m \u001b[38;5;66;03m# Get config dict associated with the base config file\u001b[39;00m\n\u001b[0;32m--> 590\u001b[0m config_dict, kwargs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mcls\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_config_dict\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpretrained_model_name_or_path\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    591\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m config_dict \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    592\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m {}, kwargs\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/transformers/configuration_utils.py:649\u001b[0m, in \u001b[0;36mPretrainedConfig._get_config_dict\u001b[0;34m(cls, pretrained_model_name_or_path, **kwargs)\u001b[0m\n\u001b[1;32m    645\u001b[0m configuration_file \u001b[38;5;241m=\u001b[39m kwargs\u001b[38;5;241m.\u001b[39mpop(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_configuration_file\u001b[39m\u001b[38;5;124m\"\u001b[39m, CONFIG_NAME) \u001b[38;5;28;01mif\u001b[39;00m gguf_file \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;28;01melse\u001b[39;00m gguf_file\n\u001b[1;32m    647\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    648\u001b[0m     \u001b[38;5;66;03m# Load from local folder or from cache or download from model Hub and cache\u001b[39;00m\n\u001b[0;32m--> 649\u001b[0m     resolved_config_file \u001b[38;5;241m=\u001b[39m \u001b[43mcached_file\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    650\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpretrained_model_name_or_path\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    651\u001b[0m \u001b[43m        \u001b[49m\u001b[43mconfiguration_file\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    652\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcache_dir\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcache_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    653\u001b[0m \u001b[43m        \u001b[49m\u001b[43mforce_download\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mforce_download\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    654\u001b[0m \u001b[43m        \u001b[49m\u001b[43mproxies\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mproxies\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    655\u001b[0m \u001b[43m        \u001b[49m\u001b[43mresume_download\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mresume_download\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    656\u001b[0m \u001b[43m        \u001b[49m\u001b[43mlocal_files_only\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlocal_files_only\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    657\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtoken\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtoken\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    658\u001b[0m \u001b[43m        \u001b[49m\u001b[43muser_agent\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43muser_agent\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    659\u001b[0m \u001b[43m        \u001b[49m\u001b[43mrevision\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrevision\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    660\u001b[0m \u001b[43m        \u001b[49m\u001b[43msubfolder\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msubfolder\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    661\u001b[0m \u001b[43m        \u001b[49m\u001b[43m_commit_hash\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcommit_hash\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    662\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    663\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m resolved_config_file \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    664\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m, kwargs\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/transformers/utils/hub.py:426\u001b[0m, in \u001b[0;36mcached_file\u001b[0;34m(path_or_repo_id, filename, cache_dir, force_download, resume_download, proxies, token, revision, local_files_only, subfolder, repo_type, user_agent, _raise_exceptions_for_gated_repo, _raise_exceptions_for_missing_entries, _raise_exceptions_for_connection_errors, _commit_hash, **deprecated_kwargs)\u001b[0m\n\u001b[1;32m    421\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mEnvironmentError\u001b[39;00m(\n\u001b[1;32m    422\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mYou are trying to access a gated repo.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124mMake sure to have access to it at \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    423\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhttps://huggingface.co/\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpath_or_repo_id\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mstr\u001b[39m(e)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    424\u001b[0m     ) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01me\u001b[39;00m\n\u001b[1;32m    425\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m RepositoryNotFoundError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m--> 426\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mEnvironmentError\u001b[39;00m(\n\u001b[1;32m    427\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpath_or_repo_id\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m is not a local folder and is not a valid model identifier \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    428\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlisted on \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mhttps://huggingface.co/models\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124mIf this is a private repository, make sure to pass a token \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    429\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhaving permission to this repo either by logging in with `huggingface-cli login` or by passing \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    430\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m`token=<your_token>`\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    431\u001b[0m     ) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01me\u001b[39;00m\n\u001b[1;32m    432\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m RevisionNotFoundError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    433\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mEnvironmentError\u001b[39;00m(\n\u001b[1;32m    434\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mrevision\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m is not a valid git identifier (branch name, tag name or commit id) that exists \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    435\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfor this model name. Check the model page at \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    436\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mhttps://huggingface.co/\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpath_or_repo_id\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m for available revisions.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    437\u001b[0m     ) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01me\u001b[39;00m\n", "\u001b[0;31mOSError\u001b[0m: facebook/regnet-y-400mf is not a local folder and is not a valid model identifier listed on 'https://huggingface.co/models'\nIf this is a private repository, make sure to pass a token having permission to this repo either by logging in with `huggingface-cli login` or by passing `token=<your_token>`"]}], "source": ["import os\n", "import numpy as np\n", "import pandas as pd\n", "import tensorflow as tf\n", "from transformers import RegNetConfig, RegNetModel\n", "from tensorflow.keras.layers import Dense, GlobalAveragePooling1D, Input, Conv1D, MaxPooling1D\n", "from tensorflow.keras.models import Model\n", "from sklearn.preprocessing import MinMaxScaler\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import confusion_matrix, classification_report\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "from scipy.signal import butter, filtfilt\n", "\n", "# Define dataset paths\n", "healthy_path = 'healthy/traindata_201911'\n", "unhealthy_path = 'unhealthy/traindata_201903'\n", "\n", "# Butterworth filter function\n", "def butter_lowpass_filter(data, cutoff=10, fs=1651, order=4):\n", "    b, a = butter(order, cutoff/(0.5*fs), btype='low')\n", "    return filtfilt(b, a, data, axis=0)\n", "\n", "# Data loading & preprocessing function (each file as an instance)\n", "def load_and_preprocess(path, label):\n", "    instances, labels = [], []\n", "    files = [f for f in os.listdir(path) if f.endswith('.xlsx')]\n", "    print(f'Loading from {path}, total files: {len(files)}')\n", "    \n", "    for file in files:\n", "        df = pd.read_excel(os.path.join(path, file)).iloc[:, :6]\n", "        \n", "        # Handling missing values\n", "        df.interpolate(method='linear', inplace=True)\n", "        data = df.values\n", "        \n", "        # Noise removal using Butterworth filter\n", "        data = butter_lowpass_filter(data)\n", "\n", "        # Normalize data\n", "        scaler = MinMaxScaler()\n", "        data = scaler.fit_transform(data)\n", "\n", "        # Ensure fixed-length: pad or trim data to exactly 40 sec (40 * 1651 = 66040 samples)\n", "        target_length = 40 * 1651\n", "        current_length = data.shape[0]\n", "        \n", "        if current_length < target_length:\n", "            padding = np.zeros((target_length - current_length, 6))\n", "            data = np.vstack((data, padding))\n", "        else:\n", "            data = data[:target_length, :]\n", "\n", "        instances.append(data)\n", "        labels.append(label)\n", "\n", "    return np.array(instances), np.array(labels)\n", "\n", "# Load both healthy and unhealthy data\n", "healthy_data, healthy_labels = load_and_preprocess(healthy_path, label=0)\n", "unhealthy_data, unhealthy_labels = load_and_preprocess(unhealthy_path, label=1)\n", "\n", "# Combine dataset\n", "X = np.concatenate([healthy_data, unhealthy_data], axis=0)\n", "y = np.concatenate([healthy_labels, unhealthy_labels], axis=0)\n", "\n", "print(f'Total dataset shape: {X.shape}, Labels shape: {y.shape}')\n", "\n", "# Train-test split\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, random_state=42, shuffle=True\n", ")\n", "\n", "# Define RegNet model from Hugging Face\n", "configuration = RegNetConfig.from_pretrained('facebook/regnet-y-400mf')\n", "model = RegNetModel.from_pretrained('facebook/regnet-y-400mf', config=configuration)\n", "\n", "# Define input shape for the time-series data\n", "input_shape = (X_train.shape[1], X_train.shape[2])  # Time-series shape: (samples, features)\n", "\n", "# Reshape data for RegNet input (channel dimension)\n", "input_tensor = Input(shape=input_shape)\n", "\n", "# Expand dimensions to simulate 2D image channels, RegNet expects 4D input (batch_size, height, width, channels)\n", "x_expanded = tf.expand_dims(input_tensor, axis=-1)  # shape: (batch, length, channels=6, 1)\n", "\n", "# Apply the RegNet base model (this will be used as a feature extractor)\n", "x = model(x_expanded).last_hidden_state\n", "\n", "# Optionally, apply CNN layers to further process the time-series data\n", "x = Conv1D(64, 3, activation='relu')(x)\n", "x = MaxPooling1D(2)(x)\n", "x = GlobalAveragePooling1D()(x)\n", "\n", "# Classification layer\n", "output = Dense(1, activation='sigmoid')(x)\n", "\n", "# Build the complete model\n", "final_model = Model(inputs=input_tensor, outputs=output)\n", "\n", "# Compile the model\n", "final_model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])\n", "final_model.summary()\n", "\n", "# Train the model\n", "history = final_model.fit(\n", "    X_train, y_train, \n", "    epochs=20, batch_size=8, \n", "    validation_split=0.2, verbose=1\n", ")\n", "\n", "# Evaluate the model\n", "loss, acc = final_model.evaluate(X_test, y_test, verbose=0)\n", "print(f\"Test Accuracy: {acc*100:.2f}%\")\n", "\n", "# Predictions and evaluation metrics\n", "y_pred = (final_model.predict(X_test) > 0.5).astype(int)\n", "\n", "# Confusion matrix visualization\n", "cm = confusion_matrix(y_test, y_pred)\n", "plt.figure(figsize=(6, 5))\n", "sns.heatmap(cm, annot=True, fmt=\"d\", cmap='Blues', xticklabels=['Healthy', 'Unhealthy'], yticklabels=['Healthy', 'Unhealthy'])\n", "plt.title('Confusion Matrix')\n", "plt.ylabel('Actual')\n", "plt.xlabel('Predicted')\n", "plt.show()\n", "\n", "# Classification Report\n", "print(\"Classification Report:\")\n", "print(classification_report(y_test, y_pred, target_names=['Healthy', 'Unhealthy']))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading from healthy/traindata_201911, total files: 59\n", "Loading from unhealthy/traindata_201903, total files: 62\n", "Total dataset shape: (121, 66040, 6), Labels shape: (121,)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/dl-env/lib/python3.10/site-packages/keras/src/layers/layer.py:1381: UserWarning: Layer 'transformer_block' looks like it has unbuilt state, but <PERSON><PERSON> is not able to trace the layer `call()` in order to build it automatically. Possible causes:\n", "1. The `call()` method of your layer may be crashing. Try to `__call__()` the layer eagerly on some test input first to see if it works. E.g. `x = np.random.random((3, 4)); y = layer(x)`\n", "2. If the `call()` method is correct, then you may need to implement the `def build(self, input_shape)` method on your layer. It should create all variables used by the layer (e.g. by calling `layer.build()` on all its children layers).\n", "Exception encountered: ''Dimensions must be equal, but are 6 and 128 for '{{node add_1}} = AddV2[T=DT_FLOAT](layer_normalization_1/add_2, sequential_1/dense_1_2/BiasAdd)' with input shapes: [?,66040,6], [?,66040,128].''\n", "  warnings.warn(\n", "/Users/<USER>/dl-env/lib/python3.10/site-packages/keras/src/layers/layer.py:391: UserWarning: `build()` was called on layer 'transformer_block', however the layer does not have a `build()` method implemented and it looks like it has unbuilt state. This will cause the layer to be marked as built, despite not being actually built, which may cause failures down the line. Make sure to implement a proper `build()` method.\n", "  warnings.warn(\n"]}, {"ename": "ValueError", "evalue": "Exception encountered when calling TransformerBlock.call().\n\n\u001b[1mCould not automatically infer the output shape / dtype of 'transformer_block' (of type TransformerBlock). Either the `TransformerBlock.call()` method is incorrect, or you need to implement the `TransformerBlock.compute_output_spec() / compute_output_shape()` method. Error encountered:\n\nDimensions must be equal, but are 6 and 128 for '{{node add_1}} = AddV2[T=DT_FLOAT](layer_normalization_1/add_2, sequential_1/dense_1_2/BiasAdd)' with input shapes: [?,66040,6], [?,66040,128].\u001b[0m\n\nArguments received by TransformerBlock.call():\n  • args=('<KerasTensor shape=(None, 66040, 6), dtype=float32, sparse=False, name=keras_tensor>',)\n  • kwargs=<class 'inspect._empty'>", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[4], line 97\u001b[0m\n\u001b[1;32m     94\u001b[0m input_tensor \u001b[38;5;241m=\u001b[39m Input(shape\u001b[38;5;241m=\u001b[39minput_shape)\n\u001b[1;32m     96\u001b[0m \u001b[38;5;66;03m# Transformer Block\u001b[39;00m\n\u001b[0;32m---> 97\u001b[0m x \u001b[38;5;241m=\u001b[39m \u001b[43mTransformerBlock\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnum_heads\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m4\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkey_dim\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m64\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m(\u001b[49m\u001b[43minput_tensor\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     99\u001b[0m \u001b[38;5;66;03m# Global Average Pooling\u001b[39;00m\n\u001b[1;32m    100\u001b[0m x \u001b[38;5;241m=\u001b[39m GlobalAveragePooling1D()(x)\n", "File \u001b[0;32m~/dl-env/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py:122\u001b[0m, in \u001b[0;36mfilter_traceback.<locals>.error_handler\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    119\u001b[0m     filtered_tb \u001b[38;5;241m=\u001b[39m _process_traceback_frames(e\u001b[38;5;241m.\u001b[39m__traceback__)\n\u001b[1;32m    120\u001b[0m     \u001b[38;5;66;03m# To get the full stack trace, call:\u001b[39;00m\n\u001b[1;32m    121\u001b[0m     \u001b[38;5;66;03m# `keras.config.disable_traceback_filtering()`\u001b[39;00m\n\u001b[0;32m--> 122\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m e\u001b[38;5;241m.\u001b[39mwith_traceback(filtered_tb) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;28;01mN<PERSON>\u001b[39;00m\n\u001b[1;32m    123\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[1;32m    124\u001b[0m     \u001b[38;5;28;01mdel\u001b[39;00m filtered_tb\n", "Cell \u001b[0;32mIn[4], line 91\u001b[0m, in \u001b[0;36mTransformerBlock.call\u001b[0;34m(self, inputs)\u001b[0m\n\u001b[1;32m     88\u001b[0m attention_output \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mnorm1(inputs \u001b[38;5;241m+\u001b[39m attention_output)\n\u001b[1;32m     90\u001b[0m ffn_output \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mffn(attention_output)\n\u001b[0;32m---> 91\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mnorm2(\u001b[43mattention_output\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mffn_output\u001b[49m)\n", "\u001b[0;31mValueError\u001b[0m: Exception encountered when calling TransformerBlock.call().\n\n\u001b[1mCould not automatically infer the output shape / dtype of 'transformer_block' (of type TransformerBlock). Either the `TransformerBlock.call()` method is incorrect, or you need to implement the `TransformerBlock.compute_output_spec() / compute_output_shape()` method. Error encountered:\n\nDimensions must be equal, but are 6 and 128 for '{{node add_1}} = AddV2[T=DT_FLOAT](layer_normalization_1/add_2, sequential_1/dense_1_2/BiasAdd)' with input shapes: [?,66040,6], [?,66040,128].\u001b[0m\n\nArguments received by TransformerBlock.call():\n  • args=('<KerasTensor shape=(None, 66040, 6), dtype=float32, sparse=False, name=keras_tensor>',)\n  • kwargs=<class 'inspect._empty'>"]}], "source": ["import os\n", "import numpy as np\n", "import pandas as pd\n", "import tensorflow as tf\n", "from tensorflow.keras.layers import Dense, Input, GlobalAveragePooling1D, Dropout, LayerNormalization\n", "from tensorflow.keras.models import Model\n", "from sklearn.preprocessing import MinMaxScaler\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import confusion_matrix, classification_report\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "from scipy.signal import butter, filtfilt\n", "\n", "\n", "\n", "# Butterworth filter function\n", "def butter_lowpass_filter(data, cutoff=10, fs=1651, order=4):\n", "    b, a = butter(order, cutoff/(0.5*fs), btype='low')\n", "    return filtfilt(b, a, data, axis=0)\n", "\n", "# Data loading & preprocessing function (each file as an instance)\n", "def load_and_preprocess(path, label):\n", "    instances, labels = [], []\n", "    files = [f for f in os.listdir(path) if f.endswith('.xlsx')]\n", "    print(f'Loading from {path}, total files: {len(files)}')\n", "    \n", "    for file in files:\n", "        df = pd.read_excel(os.path.join(path, file)).iloc[:, :6]\n", "        \n", "        # Handling missing values\n", "        df.interpolate(method='linear', inplace=True)\n", "        data = df.values\n", "        \n", "        # Noise removal using Butterworth filter\n", "        data = butter_lowpass_filter(data)\n", "\n", "        # Normalize data\n", "        scaler = MinMaxScaler()\n", "        data = scaler.fit_transform(data)\n", "\n", "        # Ensure fixed-length: pad or trim data to exactly 40 sec (40 * 1651 = 66040 samples)\n", "        target_length = 40 * 1651\n", "        current_length = data.shape[0]\n", "        \n", "        if current_length < target_length:\n", "            padding = np.zeros((target_length - current_length, 6))\n", "            data = np.vstack((data, padding))\n", "        else:\n", "            data = data[:target_length, :]\n", "\n", "        instances.append(data)\n", "        labels.append(label)\n", "\n", "    return np.array(instances), np.array(labels)\n", "\n", "# Load both healthy and unhealthy data\n", "healthy_data, healthy_labels = load_and_preprocess(healthy_path, label=0)\n", "unhealthy_data, unhealthy_labels = load_and_preprocess(unhealthy_path, label=1)\n", "\n", "# Combine dataset\n", "X = np.concatenate([healthy_data, unhealthy_data], axis=0)\n", "y = np.concatenate([healthy_labels, unhealthy_labels], axis=0)\n", "\n", "print(f'Total dataset shape: {X.shape}, Labels shape: {y.shape}')\n", "\n", "# Train-test split\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, random_state=42, shuffle=True\n", ")\n", "\n", "# Define input shape for the time-series data\n", "input_shape = (X_train.shape[1], X_train.shape[2])  # Time-series shape: (samples, features)\n", "\n", "# Transformer Model (Custom for Time-Series Data)\n", "class TransformerBlock(tf.keras.layers.Layer):\n", "    def __init__(self, num_heads, key_dim):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.attention = tf.keras.layers.MultiHeadAttention(num_heads=num_heads, key_dim=key_dim)\n", "        self.norm1 = LayerNormalization()\n", "        self.norm2 = LayerNormalization()\n", "        self.ffn = tf.keras.Sequential([\n", "            Dense(256, activation=\"relu\"),\n", "            <PERSON><PERSON>(128)\n", "        ])\n", "    \n", "    def call(self, inputs):\n", "        attention_output = self.attention(inputs, inputs)\n", "        attention_output = self.norm1(inputs + attention_output)\n", "        \n", "        ffn_output = self.ffn(attention_output)\n", "        return self.norm2(attention_output + ffn_output)\n", "\n", "# Input Layer\n", "input_tensor = Input(shape=input_shape)\n", "\n", "# Transformer Block\n", "x = TransformerBlock(num_heads=4, key_dim=64)(input_tensor)\n", "\n", "# Global Average Pooling\n", "x = GlobalAveragePooling1D()(x)\n", "\n", "# Dropout for regularization\n", "x = Dropout(0.3)(x)\n", "\n", "# Output Layer for Binary Classification\n", "output = Dense(1, activation='sigmoid')(x)\n", "\n", "# Build the model\n", "model = Model(inputs=input_tensor, outputs=output)\n", "\n", "# Compile the model\n", "model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])\n", "model.summary()\n", "\n", "# Train the model\n", "history = model.fit(\n", "    X_train, y_train, \n", "    epochs=20, batch_size=8, \n", "    validation_split=0.2, verbose=1\n", ")\n", "\n", "# Evaluate the model\n", "loss, acc = model.evaluate(X_test, y_test, verbose=0)\n", "print(f\"Test Accuracy: {acc*100:.2f}%\")\n", "\n", "# Predictions and evaluation metrics\n", "y_pred = (model.predict(X_test) > 0.5).astype(int)\n", "\n", "# Confusion matrix visualization\n", "cm = confusion_matrix(y_test, y_pred)\n", "plt.figure(figsize=(6, 5))\n", "sns.heatmap(cm, annot=True, fmt=\"d\", cmap='Blues', xticklabels=['Healthy', 'Unhealthy'], yticklabels=['Healthy', 'Unhealthy'])\n", "plt.title('Confusion Matrix')\n", "plt.ylabel('Actual')\n", "plt.xlabel('Predicted')\n", "plt.show()\n", "\n", "# Classification Report\n", "print(\"Classification Report:\")\n", "print(classification_report(y_test, y_pred, target_names=['Healthy', 'Unhealthy']))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading from healthy/traindata_201911, total files: 59\n", "Loading from unhealthy/traindata_201903, total files: 62\n", "Total dataset shape: (121, 66040, 6), Labels shape: (121,)\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">Model: \"functional_5\"</span>\n", "</pre>\n"], "text/plain": ["\u001b[1mModel: \"functional_5\"\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> Layer (type)                    </span>┃<span style=\"font-weight: bold\"> Output Shape           </span>┃<span style=\"font-weight: bold\">       Param # </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩\n", "│ input_layer_6 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">InputLayer</span>)      │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">66040</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">6</span>)       │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ transformer_block_3             │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">66040</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)      │        <span style=\"color: #00af00; text-decoration-color: #00af00\">83,648</span> │\n", "│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">TransformerBlock</span>)              │                        │               │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ global_average_pooling1d_1      │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)             │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">GlobalAveragePooling1D</span>)        │                        │               │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dropout_5 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dropout</span>)             │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)             │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_11 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)                │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">1</span>)              │            <span style=\"color: #00af00; text-decoration-color: #00af00\">65</span> │\n", "└─────────────────────────────────┴────────────────────────┴───────────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mLayer (type)                   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mOutput Shape          \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1m      Param #\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩\n", "│ input_layer_6 (\u001b[38;5;33mInputLayer\u001b[0m)      │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m66040\u001b[0m, \u001b[38;5;34m6\u001b[0m)       │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ transformer_block_3             │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m66040\u001b[0m, \u001b[38;5;34m64\u001b[0m)      │        \u001b[38;5;34m83,648\u001b[0m │\n", "│ (\u001b[38;5;33mTransformerBlock\u001b[0m)              │                        │               │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ global_average_pooling1d_1      │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m64\u001b[0m)             │             \u001b[38;5;34m0\u001b[0m │\n", "│ (\u001b[38;5;33mGlobalAveragePooling1D\u001b[0m)        │                        │               │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dropout_5 (\u001b[38;5;33mDropout\u001b[0m)             │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m64\u001b[0m)             │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_11 (\u001b[38;5;33mDense\u001b[0m)                │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m1\u001b[0m)              │            \u001b[38;5;34m65\u001b[0m │\n", "└─────────────────────────────────┴────────────────────────┴───────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Total params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">83,713</span> (327.00 KB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Total params: \u001b[0m\u001b[38;5;34m83,713\u001b[0m (327.00 KB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">83,713</span> (327.00 KB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Trainable params: \u001b[0m\u001b[38;5;34m83,713\u001b[0m (327.00 KB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Non-trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> (0.00 B)\n", "</pre>\n"], "text/plain": ["\u001b[1m Non-trainable params: \u001b[0m\u001b[38;5;34m0\u001b[0m (0.00 B)\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 1/20\n"]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m<PERSON><PERSON> crashed while executing code in the current cell or a previous cell. \n", "\u001b[1;31m<PERSON><PERSON>se review the code in the cell(s) to identify a possible cause of the failure. \n", "\u001b[1;31mClick <a href='https://aka.ms/vscodeJupyterKernelCrash'>here</a> for more info. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["import os\n", "import numpy as np\n", "import pandas as pd\n", "import tensorflow as tf\n", "from tensorflow.keras.layers import Dense, Input, GlobalAveragePooling1D, Dropout, LayerNormalization\n", "from tensorflow.keras.models import Model\n", "from sklearn.preprocessing import MinMaxScaler\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import confusion_matrix, classification_report\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "from scipy.signal import butter, filtfilt\n", "\n", "\n", "# Butterworth filter function\n", "def butter_lowpass_filter(data, cutoff=10, fs=1651, order=4):\n", "    b, a = butter(order, cutoff/(0.5*fs), btype='low')\n", "    return filtfilt(b, a, data, axis=0)\n", "\n", "# Data loading & preprocessing function (each file as an instance)\n", "def load_and_preprocess(path, label):\n", "    instances, labels = [], []\n", "    files = [f for f in os.listdir(path) if f.endswith('.xlsx')]\n", "    print(f'Loading from {path}, total files: {len(files)}')\n", "    \n", "    for file in files:\n", "        df = pd.read_excel(os.path.join(path, file)).iloc[:, :6]\n", "        \n", "        # Handling missing values\n", "        df.interpolate(method='linear', inplace=True)\n", "        data = df.values\n", "        \n", "        # Noise removal using Butterworth filter\n", "        data = butter_lowpass_filter(data)\n", "\n", "        # Normalize data\n", "        scaler = MinMaxScaler()\n", "        data = scaler.fit_transform(data)\n", "\n", "        # Ensure fixed-length: pad or trim data to exactly 40 sec (40 * 1651 = 66040 samples)\n", "        target_length = 40 * 1651\n", "        current_length = data.shape[0]\n", "        \n", "        if current_length < target_length:\n", "            padding = np.zeros((target_length - current_length, 6))\n", "            data = np.vstack((data, padding))\n", "        else:\n", "            data = data[:target_length, :]\n", "\n", "        instances.append(data)\n", "        labels.append(label)\n", "\n", "    return np.array(instances), np.array(labels)\n", "\n", "# Load both healthy and unhealthy data\n", "healthy_data, healthy_labels = load_and_preprocess(healthy_path, label=0)\n", "unhealthy_data, unhealthy_labels = load_and_preprocess(unhealthy_path, label=1)\n", "\n", "# Combine dataset\n", "X = np.concatenate([healthy_data, unhealthy_data], axis=0)\n", "y = np.concatenate([healthy_labels, unhealthy_labels], axis=0)\n", "\n", "print(f'Total dataset shape: {X.shape}, Labels shape: {y.shape}')\n", "\n", "# Train-test split\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, random_state=42, shuffle=True\n", ")\n", "\n", "# Define input shape for the time-series data\n", "input_shape = (X_train.shape[1], X_train.shape[2])  # Time-series shape: (samples, features)\n", "\n", "class TransformerBlock(tf.keras.layers.Layer):\n", "    def __init__(self, num_heads, key_dim, ff_dim):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.attention = tf.keras.layers.MultiHeadAttention(\n", "            num_heads=num_heads, \n", "            key_dim=key_dim\n", "        )\n", "        self.norm1 = LayerNormalization()\n", "        self.norm2 = LayerNormalization()\n", "        \n", "        # Add a projection layer to match dimensions before attention\n", "        self.input_projection = Dense(key_dim)\n", "        \n", "        # Ensure FFN output matches input dimension\n", "        self.ffn = tf.keras.Sequential([\n", "            Dense(ff_dim, activation=\"relu\"),\n", "            Dense(key_dim)  # Match the projected dimension\n", "        ])\n", "    \n", "    def call(self, inputs):\n", "        # Project inputs to match key_dim\n", "        projected_inputs = self.input_projection(inputs)\n", "        \n", "        # Multi-head attention\n", "        attention_output = self.attention(projected_inputs, projected_inputs)\n", "        attention_output = self.norm1(projected_inputs + attention_output)\n", "        \n", "        # Feed-forward network\n", "        ffn_output = self.ffn(attention_output)\n", "        return self.norm2(attention_output + ffn_output)\n", "\n", "# Usage\n", "input_tensor = Input(shape=input_shape)\n", "\n", "# Transformer Block with matching dimensions\n", "x = TransformerBlock(num_heads=4, key_dim=64, ff_dim=128)(input_tensor)\n", "\n", "# Global Average Pooling\n", "x = GlobalAveragePooling1D()(x)\n", "\n", "# Dropout for regularization\n", "x = Dropout(0.3)(x)\n", "\n", "# Output Layer for Binary Classification\n", "output = Dense(1, activation='sigmoid')(x)\n", "\n", "# Build the model\n", "model = Model(inputs=input_tensor, outputs=output)\n", "\n", "# Compile the model\n", "model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])\n", "model.summary()\n", "\n", "# Train the model\n", "history = model.fit(\n", "    X_train, y_train, \n", "    epochs=20, batch_size=8, \n", "    validation_split=0.2, verbose=1\n", ")\n", "\n", "# Evaluate the model\n", "loss, acc = model.evaluate(X_test, y_test, verbose=0)\n", "print(f\"Test Accuracy: {acc*100:.2f}%\")\n", "\n", "# Predictions and evaluation metrics\n", "y_pred = (model.predict(X_test) > 0.5).astype(int)\n", "\n", "# Confusion matrix visualization\n", "cm = confusion_matrix(y_test, y_pred)\n", "plt.figure(figsize=(6, 5))\n", "sns.heatmap(cm, annot=True, fmt=\"d\", cmap='Blues', xticklabels=['Healthy', 'Unhealthy'], yticklabels=['Healthy', 'Unhealthy'])\n", "plt.title('Confusion Matrix')\n", "plt.ylabel('Actual')\n", "plt.xlabel('Predicted')\n", "plt.show()\n", "\n", "# Classification Report\n", "print(\"Classification Report:\")\n", "print(classification_report(y_test, y_pred, target_names=['Healthy', 'Unhealthy']))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["class TransformerBlock(tf.keras.layers.Layer):\n", "    def __init__(self, num_heads, key_dim, ff_dim):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.attention = tf.keras.layers.MultiHeadAttention(\n", "            num_heads=num_heads, \n", "            key_dim=key_dim\n", "        )\n", "        self.norm1 = LayerNormalization()\n", "        self.norm2 = LayerNormalization()\n", "        \n", "        # Add a projection layer to match dimensions before attention\n", "        self.input_projection = Dense(key_dim)\n", "        \n", "        # Ensure FFN output matches input dimension\n", "        self.ffn = tf.keras.Sequential([\n", "            Dense(ff_dim, activation=\"relu\"),\n", "            Dense(key_dim)  # Match the projected dimension\n", "        ])\n", "    \n", "    def call(self, inputs):\n", "        # Project inputs to match key_dim\n", "        projected_inputs = self.input_projection(inputs)\n", "        \n", "        # Multi-head attention\n", "        attention_output = self.attention(projected_inputs, projected_inputs)\n", "        attention_output = self.norm1(projected_inputs + attention_output)\n", "        \n", "        # Feed-forward network\n", "        ffn_output = self.ffn(attention_output)\n", "        return self.norm2(attention_output + ffn_output)\n", "\n", "# Usage\n", "input_tensor = Input(shape=input_shape)\n", "\n", "# Transformer Block with matching dimensions\n", "x = TransformerBlock(num_heads=4, key_dim=64, ff_dim=128)(input_tensor)\n", "\n", "# Global Average Pooling\n", "x = GlobalAveragePooling1D()(x)\n", "\n", "# Dropout for regularization\n", "x = Dropout(0.3)(x)\n", "\n", "# Output Layer for Binary Classification\n", "output = Dense(1, activation='sigmoid')(x)\n", "\n", "# Build the model\n", "model = Model(inputs=input_tensor, outputs=output)\n", "\n", "# Compile the model\n", "model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "dl-env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 2}