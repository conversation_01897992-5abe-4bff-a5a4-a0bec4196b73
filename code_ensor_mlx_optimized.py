#!/usr/bin/env python3
"""
MLX-Optimized Bridge Health Monitoring for Apple Silicon
Maximum performance using Apple's MLX framework
"""

import os
import pandas as pd
import numpy as np
import time
import json

# MLX imports for Apple Silicon
try:
    import mlx.core as mx
    import mlx.nn as nn
    import mlx.optimizers as optim
    from mlx.utils import tree_flatten, tree_unflatten
    MLX_AVAILABLE = True
    print("🍎 MLX optimization enabled!")
except ImportError:
    MLX_AVAILABLE = False
    print("❌ MLX not available. Install with: pip install mlx")
    exit(1)

# Core libraries
from scipy.fft import fft
from scipy.stats import kurtosis, skew
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt

class MLXBridgeClassifier(nn.Module):
    """MLX-optimized neural network for bridge health classification"""
    
    def __init__(self, input_dim: int, hidden_dim: int = 128):
        super().__init__()
        self.layers = [
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.ReLU(),
            nn.Linear(hidden_dim // 4, 2)  # Binary classification
        ]
    
    def __call__(self, x):
        for layer in self.layers:
            x = layer(x)
        return x

def mlx_feature_extraction(signal_batch, sampling_freq=1651):
    """MLX-accelerated feature extraction"""
    # Convert to MLX array for GPU acceleration
    signal_mx = mx.array(signal_batch)
    
    features = {}
    
    # Time domain features (MLX accelerated)
    features['rms'] = mx.sqrt(mx.mean(signal_mx**2, axis=1))
    features['peak_to_peak'] = mx.max(signal_mx, axis=1) - mx.min(signal_mx, axis=1)
    features['energy'] = mx.sum(signal_mx**2, axis=1)
    features['std'] = mx.std(signal_mx, axis=1)
    features['mean'] = mx.mean(signal_mx, axis=1)
    features['var'] = mx.var(signal_mx, axis=1)
    
    # Advanced statistical features
    features['mean_abs'] = mx.mean(mx.abs(signal_mx), axis=1)
    features['max_abs'] = mx.max(mx.abs(signal_mx), axis=1)
    
    # Crest factor
    features['crest_factor'] = features['max_abs'] / (features['rms'] + 1e-10)
    
    # Shape factor
    features['shape_factor'] = features['rms'] / (features['mean_abs'] + 1e-10)
    
    # Convert back to numpy for scipy functions
    signal_np = np.array(signal_mx)
    
    # Frequency domain features
    fft_features = []
    for i, signal in enumerate(signal_np):
        fft_vals = fft(signal)
        fft_magnitude = np.abs(fft_vals)[:len(signal)//2]
        freqs = np.fft.fftfreq(len(signal), 1/sampling_freq)[:len(signal)//2]
        
        if len(fft_magnitude) > 0:
            dominant_freq = freqs[np.argmax(fft_magnitude)]
            spectral_power = np.sum(fft_magnitude**2) / len(signal)
            spectral_centroid = np.sum(freqs * fft_magnitude) / (np.sum(fft_magnitude) + 1e-10)
        else:
            dominant_freq = spectral_power = spectral_centroid = 0
        
        # Statistical features on frequency domain
        kurt_val = kurtosis(signal, nan_policy='omit')
        skew_val = skew(signal, nan_policy='omit')
        
        fft_features.append([dominant_freq, spectral_power, spectral_centroid, kurt_val, skew_val])
    
    fft_features_mx = mx.array(fft_features)
    features['dominant_freq'] = fft_features_mx[:, 0]
    features['spectral_power'] = fft_features_mx[:, 1]
    features['spectral_centroid'] = fft_features_mx[:, 2]
    features['kurtosis'] = fft_features_mx[:, 3]
    features['skewness'] = fft_features_mx[:, 4]
    
    # Flatten all features into a matrix
    feature_matrix = mx.stack([features[key] for key in features.keys()], axis=1)
    
    return feature_matrix

def load_and_process_data_mlx(base_path, max_files=10):
    """MLX-optimized data loading and processing"""
    print("🍎 MLX-optimized data loading...")
    
    # Load healthy data
    healthy_path = os.path.join(base_path, "healthy")
    unhealthy_path = os.path.join(base_path, "unhealthy")
    
    all_features = []
    all_labels = []
    
    # Process healthy files
    if os.path.exists(healthy_path):
        healthy_files = [f for f in os.listdir(healthy_path) if f.endswith('.xlsx')][:max_files]
        print(f"📊 Processing {len(healthy_files)} healthy files...")
        
        for i, file in enumerate(healthy_files):
            try:
                df = pd.read_excel(os.path.join(healthy_path, file), usecols=range(6))
                print(f"✅ Healthy {i+1}/{len(healthy_files)}: {file}")
                
                # Extract windows from each sensor
                window_size = 1651  # 1 second windows
                start_idx = 2 * 1651  # Skip first 2 seconds
                
                for sensor in range(6):
                    sensor_data = df.iloc[start_idx:, sensor].values
                    
                    # Create overlapping windows
                    windows = []
                    for j in range(0, len(sensor_data) - window_size, window_size // 2):
                        if j + window_size <= len(sensor_data):
                            windows.append(sensor_data[j:j + window_size])
                    
                    if windows:
                        # Batch process windows with MLX
                        windows_array = np.array(windows)
                        features = mlx_feature_extraction(windows_array)
                        
                        all_features.append(features)
                        all_labels.extend([1] * len(windows))  # Healthy = 1
                        
            except Exception as e:
                print(f"❌ Error processing {file}: {e}")
    
    # Process unhealthy files
    if os.path.exists(unhealthy_path):
        unhealthy_files = []
        for root, _, files in os.walk(unhealthy_path):
            unhealthy_files.extend([os.path.join(root, f) for f in files if f.endswith('.xlsx')])
        
        unhealthy_files = unhealthy_files[:max_files]
        print(f"📊 Processing {len(unhealthy_files)} unhealthy files...")
        
        for i, file_path in enumerate(unhealthy_files):
            try:
                df = pd.read_excel(file_path, usecols=range(6))
                print(f"✅ Unhealthy {i+1}/{len(unhealthy_files)}: {os.path.basename(file_path)}")
                
                window_size = 1651
                start_idx = 2 * 1651
                
                for sensor in range(6):
                    sensor_data = df.iloc[start_idx:, sensor].values
                    
                    windows = []
                    for j in range(0, len(sensor_data) - window_size, window_size // 2):
                        if j + window_size <= len(sensor_data):
                            windows.append(sensor_data[j:j + window_size])
                    
                    if windows:
                        windows_array = np.array(windows)
                        features = mlx_feature_extraction(windows_array)
                        
                        all_features.append(features)
                        all_labels.extend([0] * len(windows))  # Unhealthy = 0
                        
            except Exception as e:
                print(f"❌ Error processing {os.path.basename(file_path)}: {e}")
    
    # Combine all features
    if all_features:
        X = mx.concatenate(all_features, axis=0)
        y = mx.array(all_labels)
        return X, y
    else:
        return None, None

def train_mlx_model(X, y, epochs=200, learning_rate=0.001):
    """Train MLX neural network"""
    print("🧠 Training MLX neural network...")
    
    # Initialize model
    model = MLXBridgeClassifier(X.shape[1])
    optimizer = optim.Adam(learning_rate=learning_rate)
    
    def loss_fn(model, X, y):
        logits = model(X)
        return nn.losses.cross_entropy(logits, y, reduction='mean')
    
    loss_and_grad_fn = nn.value_and_grad(model, loss_fn)
    
    # Training loop
    for epoch in range(epochs):
        loss, grads = loss_and_grad_fn(model, X, y)
        optimizer.update(model, grads)
        mx.eval(model.parameters(), optimizer.state)
        
        if epoch % 50 == 0:
            print(f"Epoch {epoch}: Loss = {loss.item():.4f}")
    
    return model

def evaluate_mlx_model(model, X_test, y_test):
    """Evaluate MLX model"""
    logits = model(X_test)
    predictions = mx.argmax(logits, axis=1)
    probabilities = nn.softmax(logits, axis=1)
    
    # Convert to numpy for sklearn metrics
    y_pred = np.array(predictions)
    y_true = np.array(y_test)
    y_prob = np.array(probabilities)
    
    accuracy = accuracy_score(y_true, y_pred)
    precision = precision_score(y_true, y_pred)
    recall = recall_score(y_true, y_pred)
    f1 = f1_score(y_true, y_pred)
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'predictions': y_pred,
        'probabilities': y_prob
    }

def main_mlx():
    """Main MLX-optimized pipeline"""
    print("🍎" + "="*60)
    print("MLX-OPTIMIZED BRIDGE HEALTH MONITORING")
    print("Maximum Apple Silicon Performance")
    print("🍎" + "="*60)
    
    start_time = time.time()
    base_path = "/Users/<USER>/Documents/bridge_sensor/data/"
    os.makedirs("mlx_results", exist_ok=True)
    
    # Load and process data
    print("\n🚀 1. MLX DATA PROCESSING")
    print("-" * 30)
    X, y = load_and_process_data_mlx(base_path, max_files=8)
    
    if X is None:
        print("❌ No data loaded")
        return
    
    print(f"✅ Data loaded: {X.shape[0]} samples, {X.shape[1]} features")
    print(f"📊 Class distribution: Healthy={mx.sum(y).item()}, Unhealthy={len(y) - mx.sum(y).item()}")
    
    # Train-test split
    X_np = np.array(X)
    y_np = np.array(y)
    
    X_train, X_test, y_train, y_test = train_test_split(
        X_np, y_np, test_size=0.2, random_state=42, stratify=y_np
    )
    
    # Convert back to MLX
    X_train_mx = mx.array(X_train.astype(np.float32))
    X_test_mx = mx.array(X_test.astype(np.float32))
    y_train_mx = mx.array(y_train.astype(np.int32))
    y_test_mx = mx.array(y_test.astype(np.int32))
    
    # Normalize features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    X_train_mx = mx.array(X_train_scaled.astype(np.float32))
    X_test_mx = mx.array(X_test_scaled.astype(np.float32))
    
    print(f"📈 Training set: {X_train_mx.shape}")
    print(f"📉 Test set: {X_test_mx.shape}")
    
    # Train model
    print("\n🧠 2. MLX NEURAL NETWORK TRAINING")
    print("-" * 40)
    model = train_mlx_model(X_train_mx, y_train_mx)
    
    # Evaluate model
    print("\n📊 3. MODEL EVALUATION")
    print("-" * 25)
    results = evaluate_mlx_model(model, X_test_mx, y_test_mx)
    
    training_time = time.time() - start_time
    
    print(f"✅ Accuracy: {results['accuracy']:.4f}")
    print(f"✅ Precision: {results['precision']:.4f}")
    print(f"✅ Recall: {results['recall']:.4f}")
    print(f"✅ F1-Score: {results['f1_score']:.4f}")
    
    # Save results
    summary = {
        'training_time': training_time,
        'mlx_optimized': True,
        'apple_silicon': True,
        'samples_processed': int(X.shape[0]),
        'features_extracted': int(X.shape[1]),
        'performance': results
    }
    
    # Remove non-serializable items
    summary['performance'] = {k: v for k, v in results.items() 
                            if k not in ['predictions', 'probabilities']}
    
    with open('mlx_results/mlx_results.json', 'w') as f:
        json.dump(summary, f, indent=2, default=str)
    
    print(f"\n🍎" + "="*60)
    print("MLX OPTIMIZATION COMPLETED! 🎉")
    print(f"⏱️  Total time: {training_time:.2f} seconds")
    print(f"🏆 F1-Score: {results['f1_score']:.4f}")
    print(f"💾 Results saved to mlx_results/")
    print("🍎" + "="*60)

if __name__ == "__main__":
    main_mlx()
