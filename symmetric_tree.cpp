#include <iostream>
using namespace std;

// Definition for a binary tree node
struct TreeNode {
    int val;
    TreeNode *left;
    TreeNode *right;
    TreeNode(int x) : val(x), left(NULL), right(NULL) {}
};

class Solution {
private:
    // Helper function to check if two subtrees are mirror images
    bool isMirror(TreeNode* left, TreeNode* right) {
        // If both nodes are NULL, they are mirror images
        if (left == NULL && right == NULL) 
            return true;
        
        // If only one node is NULL, they are not mirror images
        if (left == NULL || right == NULL) 
            return false;
        
        // Check if:
        // 1. Values are equal
        // 2. Left's left matches Right's right
        // 3. Left's right matches Right's left
        return (left->val == right->val) && 
               isMirror(left->left, right->right) && 
               isMirror(left->right, right->left);
    }
    
public:
    bool isSymmetric(TreeNode* root) {
        // Empty tree is symmetric
        if (root == NULL) 
            return true;
        
        // Check if left and right subtrees are mirror images
        return isMirror(root->left, root->right);
    }
};

// Helper function to create a new node
TreeNode* newNode(int val) {
    TreeNode* node = new TreeNode(val);
    return node;
}

// Main function to test the solution
int main() {
    // Create a symmetric tree
    //      1
    //    /   \
    //   2     2
    //  / \   / \
    // 3   4 4   3
    TreeNode* root = newNode(1);
    root->left = newNode(2);
    root->right = newNode(2);
    root->left->left = newNode(3);
    root->left->right = newNode(4);
    root->right->left = newNode(4);
    root->right->right = newNode(3);
    
    Solution solution;
    if (solution.isSymmetric(root))
        cout << "The tree is symmetric" << endl;
    else
        cout << "The tree is not symmetric" << endl;
    
    // Clean up memory
    // Note: In a real application, you should implement proper memory cleanup
    return 0;
}