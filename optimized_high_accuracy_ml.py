#!/usr/bin/env python3
"""
Optimized High-Accuracy Bridge Health Monitoring (Target: 94-97% Accuracy)
Comprehensive evaluation with detailed confusion matrix and all metrics
"""

import os
import pandas as pd
import numpy as np
import time
import warnings
warnings.filterwarnings('ignore')

# Core ML imports
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold, GridSearchCV
from sklearn.preprocessing import StandardScaler, RobustScaler, QuantileTransformer, PowerTransformer
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, ExtraTreesClassifier
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import (accuracy_score, f1_score, precision_score, recall_score, roc_auc_score,
                           classification_report, confusion_matrix, roc_curve, precision_recall_curve,
                           matthews_corrcoef, cohen_kappa_score, balanced_accuracy_score)
from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif, RFE, SelectFromModel
from sklearn.utils.class_weight import compute_class_weight
from sklearn.pipeline import Pipeline
from sklearn.decomposition import PCA
import xgboost as xgb
from imblearn.over_sampling import SMOTE, ADASYN, BorderlineSMOTE
from imblearn.under_sampling import RandomUnderSampler, EditedNearestNeighbours
from imblearn.combine import SMOTETomek, SMOTEENN

# Advanced signal processing
from scipy.fft import fft, fftfreq
from scipy.stats import kurtosis, skew, entropy
from scipy.signal import welch, butter, filtfilt, hilbert, find_peaks, spectrogram
from scipy.signal.windows import hann, hamming, blackman
import pywt

# Visualization
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import seaborn as sns

def load_optimized_data(base_path):
    """Load data with strict quality control for high accuracy"""
    print("📊 Loading data with optimized quality control...")

    healthy_data = []
    unhealthy_data = []

    # Load healthy data with strict criteria
    healthy_path = os.path.join(base_path, "healthy")
    if os.path.exists(healthy_path):
        for root, dirs, files in os.walk(healthy_path):
            for file in files:
                if file.endswith('.xlsx'):
                    file_path = os.path.join(root, file)
                    try:
                        df = pd.read_excel(file_path, usecols=range(6))
                        # Strict quality criteria
                        if (len(df) > 20000 and  # Longer minimum length
                            not df.isnull().all().any() and  # No empty columns
                            df.std().min() > 1e-5 and  # Minimum variance
                            df.max().max() < 1e6 and  # No extreme outliers
                            df.min().min() > -1e6):
                            healthy_data.append(df)
                            print(f"✅ Healthy: {file}")
                        else:
                            print(f"⚠️ Skipped {file} - quality criteria not met")
                    except Exception as e:
                        print(f"❌ Error loading {file}: {e}")

    # Load unhealthy data with same strict criteria
    unhealthy_path = os.path.join(base_path, "unhealthy")
    if os.path.exists(unhealthy_path):
        for root, dirs, files in os.walk(unhealthy_path):
            for file in files:
                if file.endswith('.xlsx'):
                    file_path = os.path.join(root, file)
                    try:
                        df = pd.read_excel(file_path, usecols=range(6))
                        if (len(df) > 20000 and
                            not df.isnull().all().any() and
                            df.std().min() > 1e-5 and
                            df.max().max() < 1e6 and
                            df.min().min() > -1e6):
                            unhealthy_data.append(df)
                            print(f"✅ Unhealthy: {file}")
                        else:
                            print(f"⚠️ Skipped {file} - quality criteria not met")
                    except Exception as e:
                        print(f"❌ Error loading {file}: {e}")

    print(f"\n📊 High-quality data: {len(healthy_data)} healthy, {len(unhealthy_data)} unhealthy")
    return healthy_data, unhealthy_data

def extract_optimized_features(df, sampling_freq=1651):
    """
    Extract comprehensive optimized features for maximum accuracy
    """
    # Use longer, more stable segments
    start_idx = 5 * sampling_freq  # Skip first 5 seconds
    usable_length = len(df) - start_idx - (4 * sampling_freq)  # Leave 4 seconds at end

    if usable_length < 10 * sampling_freq:  # Need at least 10 seconds
        return []

    window_size = int(4 * sampling_freq)  # 4-second windows for more stable features
    step_size = int(2 * sampling_freq)    # 2-second step

    features_list = []

    # Create multiple windows from each file
    for window_start in range(start_idx, start_idx + usable_length - window_size, step_size):
        window_end = window_start + window_size
        window_data = df.iloc[window_start:window_end]

        window_features = {}

        # Extract comprehensive features for each sensor
        for sensor in range(6):
            sensor_data = window_data.iloc[:, sensor].values

            # Advanced preprocessing
            sensor_data = sensor_data - np.mean(sensor_data)  # Remove DC

            # Apply multiple filters for different frequency bands
            try:
                nyquist = sampling_freq / 2
                # Low frequency (0.5-20 Hz)
                low_freq = filtfilt(*butter(6, [0.5/nyquist, 20/nyquist], btype='band'), sensor_data)
                # Mid frequency (20-100 Hz)
                mid_freq = filtfilt(*butter(6, [20/nyquist, 100/nyquist], btype='band'), sensor_data)
                # High frequency (100-300 Hz)
                high_freq = filtfilt(*butter(6, [100/nyquist, 300/nyquist], btype='band'), sensor_data)
            except:
                low_freq = mid_freq = high_freq = sensor_data

            # 1. Comprehensive Time Domain Features
            for freq_band, name in [(sensor_data, 'full'), (low_freq, 'low'), (mid_freq, 'mid'), (high_freq, 'high')]:
                prefix = f's{sensor}_{name}'

                # Basic statistics
                window_features[f'{prefix}_rms'] = np.sqrt(np.mean(freq_band**2))
                window_features[f'{prefix}_std'] = np.std(freq_band)
                window_features[f'{prefix}_var'] = np.var(freq_band)
                window_features[f'{prefix}_peak2peak'] = np.max(freq_band) - np.min(freq_band)
                window_features[f'{prefix}_energy'] = np.sum(freq_band**2)
                window_features[f'{prefix}_mean_abs'] = np.mean(np.abs(freq_band))
                window_features[f'{prefix}_max_abs'] = np.max(np.abs(freq_band))

                # Advanced statistical moments
                try:
                    window_features[f'{prefix}_kurtosis'] = kurtosis(freq_band, fisher=True, nan_policy='omit')
                    window_features[f'{prefix}_skewness'] = skew(freq_band, nan_policy='omit')
                except:
                    window_features[f'{prefix}_kurtosis'] = 0.0
                    window_features[f'{prefix}_skewness'] = 0.0

                # Shape factors
                rms_val = window_features[f'{prefix}_rms']
                mean_abs = window_features[f'{prefix}_mean_abs']
                max_abs = window_features[f'{prefix}_max_abs']

                if rms_val > 1e-10 and mean_abs > 1e-10:
                    window_features[f'{prefix}_crest_factor'] = max_abs / rms_val
                    window_features[f'{prefix}_shape_factor'] = rms_val / mean_abs
                    window_features[f'{prefix}_impulse_factor'] = max_abs / mean_abs
                    window_features[f'{prefix}_clearance_factor'] = max_abs / (np.mean(np.sqrt(np.abs(freq_band)))**2)
                    window_features[f'{prefix}_k_factor'] = max_abs / (np.mean(freq_band**4)**(1/4))
                else:
                    for factor in ['crest_factor', 'shape_factor', 'impulse_factor', 'clearance_factor', 'k_factor']:
                        window_features[f'{prefix}_{factor}'] = 1.0

                # Percentile features
                window_features[f'{prefix}_p25'] = np.percentile(np.abs(freq_band), 25)
                window_features[f'{prefix}_p75'] = np.percentile(np.abs(freq_band), 75)
                window_features[f'{prefix}_p90'] = np.percentile(np.abs(freq_band), 90)
                window_features[f'{prefix}_p95'] = np.percentile(np.abs(freq_band), 95)
                window_features[f'{prefix}_p99'] = np.percentile(np.abs(freq_band), 99)

            # 2. Advanced Frequency Domain Features
            try:
                # Multiple window functions for FFT
                for window_func, win_name in [(hann, 'hann'), (hamming, 'hamm'), (blackman, 'black')]:
                    windowed_signal = sensor_data * window_func(len(sensor_data))
                    fft_vals = fft(windowed_signal)
                    fft_magnitude = np.abs(fft_vals)[:len(sensor_data)//2]
                    freqs = fftfreq(len(sensor_data), 1/sampling_freq)[:len(sensor_data)//2]

                    if len(fft_magnitude) > 0 and np.sum(fft_magnitude) > 1e-10:
                        # Spectral features
                        dom_idx = np.argmax(fft_magnitude)
                        window_features[f's{sensor}_{win_name}_dominant_freq'] = freqs[dom_idx]
                        window_features[f's{sensor}_{win_name}_dominant_mag'] = fft_magnitude[dom_idx]

                        # Spectral moments
                        total_power = np.sum(fft_magnitude)
                        window_features[f's{sensor}_{win_name}_spectral_centroid'] = np.sum(freqs * fft_magnitude) / total_power

                        centroid = window_features[f's{sensor}_{win_name}_spectral_centroid']
                        window_features[f's{sensor}_{win_name}_spectral_spread'] = np.sqrt(
                            np.sum(((freqs - centroid)**2) * fft_magnitude) / total_power
                        )

                        # Higher order spectral moments
                        window_features[f's{sensor}_{win_name}_spectral_skewness'] = np.sum(
                            ((freqs - centroid)**3) * fft_magnitude
                        ) / (total_power * window_features[f's{sensor}_{win_name}_spectral_spread']**3)

                        window_features[f's{sensor}_{win_name}_spectral_kurtosis'] = np.sum(
                            ((freqs - centroid)**4) * fft_magnitude
                        ) / (total_power * window_features[f's{sensor}_{win_name}_spectral_spread']**4)

                        # Spectral rolloff points
                        cumsum_spectrum = np.cumsum(fft_magnitude)
                        for rolloff in [0.75, 0.85, 0.95, 0.99]:
                            rolloff_idx = np.where(cumsum_spectrum >= rolloff * total_power)[0]
                            window_features[f's{sensor}_{win_name}_rolloff_{int(rolloff*100)}'] = (
                                freqs[rolloff_idx[0]] if len(rolloff_idx) > 0 else freqs[-1]
                            )

                        # Spectral entropy
                        normalized_spectrum = fft_magnitude / total_power
                        window_features[f's{sensor}_{win_name}_spectral_entropy'] = entropy(normalized_spectrum + 1e-10)

                        # Spectral flux
                        window_features[f's{sensor}_{win_name}_spectral_flux'] = np.sum(np.diff(fft_magnitude)**2)

                        # Spectral flatness
                        geometric_mean = np.exp(np.mean(np.log(fft_magnitude + 1e-10)))
                        arithmetic_mean = np.mean(fft_magnitude)
                        window_features[f's{sensor}_{win_name}_spectral_flatness'] = geometric_mean / arithmetic_mean

                    else:
                        # Default values
                        for feature in ['dominant_freq', 'dominant_mag', 'spectral_centroid', 'spectral_spread',
                                      'spectral_skewness', 'spectral_kurtosis', 'spectral_entropy', 'spectral_flux',
                                      'spectral_flatness']:
                            window_features[f's{sensor}_{win_name}_{feature}'] = 0.0
                        for rolloff in [75, 85, 95, 99]:
                            window_features[f's{sensor}_{win_name}_rolloff_{rolloff}'] = 0.0

            except Exception as e:
                # Set default values if frequency analysis fails
                for win_name in ['hann', 'hamm', 'black']:
                    for feature in ['dominant_freq', 'dominant_mag', 'spectral_centroid', 'spectral_spread',
                                  'spectral_skewness', 'spectral_kurtosis', 'spectral_entropy', 'spectral_flux',
                                  'spectral_flatness']:
                        window_features[f's{sensor}_{win_name}_{feature}'] = 0.0
                    for rolloff in [75, 85, 95, 99]:
                        window_features[f's{sensor}_{win_name}_rolloff_{rolloff}'] = 0.0

            # 3. Advanced PSD Features
            try:
                # Multiple PSD methods
                for nperseg_factor in [4, 8, 16]:
                    nperseg = min(len(sensor_data)//nperseg_factor, 2048)
                    freqs_psd, psd = welch(sensor_data, sampling_freq, nperseg=nperseg, window='hann')

                    if len(psd) > 0:
                        psd_name = f'psd_{nperseg_factor}'
                        window_features[f's{sensor}_{psd_name}_peak_freq'] = freqs_psd[np.argmax(psd)]
                        window_features[f's{sensor}_{psd_name}_peak_power'] = np.max(psd)
                        window_features[f's{sensor}_{psd_name}_mean'] = np.mean(psd)
                        window_features[f's{sensor}_{psd_name}_std'] = np.std(psd)
                        window_features[f's{sensor}_{psd_name}_total_power'] = np.sum(psd)

                        # Power in frequency bands
                        bands = [(0.5, 5), (5, 20), (20, 50), (50, 100), (100, 200), (200, 400)]
                        for low, high in bands:
                            band_mask = (freqs_psd >= low) & (freqs_psd <= high)
                            window_features[f's{sensor}_{psd_name}_power_{low}_{high}Hz'] = np.sum(psd[band_mask])
                    else:
                        psd_name = f'psd_{nperseg_factor}'
                        for feature in ['peak_freq', 'peak_power', 'mean', 'std', 'total_power']:
                            window_features[f's{sensor}_{psd_name}_{feature}'] = 0.0
                        bands = [(0.5, 5), (5, 20), (20, 50), (50, 100), (100, 200), (200, 400)]
                        for low, high in bands:
                            window_features[f's{sensor}_{psd_name}_power_{low}_{high}Hz'] = 0.0
            except:
                for nperseg_factor in [4, 8, 16]:
                    psd_name = f'psd_{nperseg_factor}'
                    for feature in ['peak_freq', 'peak_power', 'mean', 'std', 'total_power']:
                        window_features[f's{sensor}_{psd_name}_{feature}'] = 0.0
                    bands = [(0.5, 5), (5, 20), (20, 50), (50, 100), (100, 200), (200, 400)]
                    for low, high in bands:
                        window_features[f's{sensor}_{psd_name}_power_{low}_{high}Hz'] = 0.0

            # 4. Advanced Wavelet Features
            try:
                # Multiple wavelet families
                for wavelet in ['db8', 'db16', 'coif8', 'bior4.4', 'dmey']:
                    coeffs = pywt.wavedec(sensor_data, wavelet, level=7)
                    for level, coeff in enumerate(coeffs):
                        if len(coeff) > 0:
                            wav_name = f'{wavelet}_L{level}'
                            window_features[f's{sensor}_{wav_name}_energy'] = np.sum(coeff**2)
                            window_features[f's{sensor}_{wav_name}_std'] = np.std(coeff)
                            window_features[f's{sensor}_{wav_name}_mean'] = np.mean(np.abs(coeff))
                            window_features[f's{sensor}_{wav_name}_max'] = np.max(np.abs(coeff))
                            window_features[f's{sensor}_{wav_name}_entropy'] = entropy(np.abs(coeff) + 1e-10)
                        else:
                            wav_name = f'{wavelet}_L{level}'
                            for feature in ['energy', 'std', 'mean', 'max', 'entropy']:
                                window_features[f's{sensor}_{wav_name}_{feature}'] = 0.0
            except:
                for wavelet in ['db8', 'db16', 'coif8', 'bior4.4', 'dmey']:
                    for level in range(8):
                        wav_name = f'{wavelet}_L{level}'
                        for feature in ['energy', 'std', 'mean', 'max', 'entropy']:
                            window_features[f's{sensor}_{wav_name}_{feature}'] = 0.0

            # 5. Hilbert Transform Features (Envelope Analysis)
            try:
                analytic_signal = hilbert(sensor_data)
                amplitude_envelope = np.abs(analytic_signal)
                instantaneous_phase = np.unwrap(np.angle(analytic_signal))
                instantaneous_frequency = np.diff(instantaneous_phase) / (2.0 * np.pi) * sampling_freq

                window_features[f's{sensor}_envelope_mean'] = np.mean(amplitude_envelope)
                window_features[f's{sensor}_envelope_std'] = np.std(amplitude_envelope)
                window_features[f's{sensor}_envelope_max'] = np.max(amplitude_envelope)
                window_features[f's{sensor}_envelope_energy'] = np.sum(amplitude_envelope**2)
                window_features[f's{sensor}_envelope_kurtosis'] = kurtosis(amplitude_envelope, nan_policy='omit')
                window_features[f's{sensor}_inst_freq_mean'] = np.mean(instantaneous_frequency)
                window_features[f's{sensor}_inst_freq_std'] = np.std(instantaneous_frequency)
                window_features[f's{sensor}_inst_freq_max'] = np.max(instantaneous_frequency)
            except:
                for feature in ['envelope_mean', 'envelope_std', 'envelope_max', 'envelope_energy',
                              'envelope_kurtosis', 'inst_freq_mean', 'inst_freq_std', 'inst_freq_max']:
                    window_features[f's{sensor}_{feature}'] = 0.0

            # 6. Peak Analysis
            try:
                # Multiple peak detection thresholds
                for threshold_factor in [0.5, 1.0, 1.5, 2.0]:
                    threshold = threshold_factor * np.std(sensor_data)
                    peaks, properties = find_peaks(np.abs(sensor_data), height=threshold)

                    peak_name = f'peaks_{int(threshold_factor*10)}'
                    if len(peaks) > 0:
                        window_features[f's{sensor}_{peak_name}_count'] = len(peaks)
                        window_features[f's{sensor}_{peak_name}_heights_mean'] = np.mean(properties['peak_heights'])
                        window_features[f's{sensor}_{peak_name}_heights_std'] = np.std(properties['peak_heights'])
                        window_features[f's{sensor}_{peak_name}_heights_max'] = np.max(properties['peak_heights'])
                        if len(peaks) > 1:
                            window_features[f's{sensor}_{peak_name}_spacing_mean'] = np.mean(np.diff(peaks))
                            window_features[f's{sensor}_{peak_name}_spacing_std'] = np.std(np.diff(peaks))
                        else:
                            window_features[f's{sensor}_{peak_name}_spacing_mean'] = 0
                            window_features[f's{sensor}_{peak_name}_spacing_std'] = 0
                    else:
                        for feature in ['count', 'heights_mean', 'heights_std', 'heights_max', 'spacing_mean', 'spacing_std']:
                            window_features[f's{sensor}_{peak_name}_{feature}'] = 0
            except:
                for threshold_factor in [0.5, 1.0, 1.5, 2.0]:
                    peak_name = f'peaks_{int(threshold_factor*10)}'
                    for feature in ['count', 'heights_mean', 'heights_std', 'heights_max', 'spacing_mean', 'spacing_std']:
                        window_features[f's{sensor}_{peak_name}_{feature}'] = 0

        # 7. Cross-sensor features
        try:
            sensor_matrix = window_data.values

            # Correlation matrix features
            corr_matrix = np.corrcoef(sensor_matrix.T)
            for i in range(6):
                for j in range(i+1, 6):
                    window_features[f'cross_corr_s{i}_s{j}'] = corr_matrix[i, j] if not np.isnan(corr_matrix[i, j]) else 0

            # Correlation matrix statistics
            upper_triangle = corr_matrix[np.triu_indices_from(corr_matrix, k=1)]
            window_features['corr_matrix_mean'] = np.mean(upper_triangle)
            window_features['corr_matrix_std'] = np.std(upper_triangle)
            window_features['corr_matrix_max'] = np.max(upper_triangle)
            window_features['corr_matrix_min'] = np.min(upper_triangle)

            # Principal component analysis
            try:
                from sklearn.decomposition import PCA
                pca = PCA(n_components=min(6, sensor_matrix.shape[1]))
                pca_components = pca.fit_transform(sensor_matrix)

                for i, ratio in enumerate(pca.explained_variance_ratio_):
                    window_features[f'pca_explained_var_ratio_{i+1}'] = ratio

                # PCA component statistics
                for i in range(min(3, pca_components.shape[1])):
                    window_features[f'pca_comp_{i+1}_mean'] = np.mean(pca_components[:, i])
                    window_features[f'pca_comp_{i+1}_std'] = np.std(pca_components[:, i])
                    window_features[f'pca_comp_{i+1}_energy'] = np.sum(pca_components[:, i]**2)
            except:
                for i in range(6):
                    window_features[f'pca_explained_var_ratio_{i+1}'] = 0
                for i in range(3):
                    for feature in ['mean', 'std', 'energy']:
                        window_features[f'pca_comp_{i+1}_{feature}'] = 0

        except:
            # Default cross-sensor features
            for i in range(6):
                for j in range(i+1, 6):
                    window_features[f'cross_corr_s{i}_s{j}'] = 0
            for feature in ['corr_matrix_mean', 'corr_matrix_std', 'corr_matrix_max', 'corr_matrix_min']:
                window_features[feature] = 0
            for i in range(6):
                window_features[f'pca_explained_var_ratio_{i+1}'] = 0
            for i in range(3):
                for feature in ['mean', 'std', 'energy']:
                    window_features[f'pca_comp_{i+1}_{feature}'] = 0

        features_list.append(window_features)

    return features_list

def create_optimized_models():
    """Create highly optimized models for maximum accuracy"""
    models = {
        'XGBoost_Ultra': xgb.XGBClassifier(
            n_estimators=800,
            max_depth=8,
            learning_rate=0.05,
            subsample=0.8,
            colsample_bytree=0.8,
            reg_alpha=0.1,
            reg_lambda=0.1,
            gamma=0.1,
            min_child_weight=3,
            random_state=42,
            eval_metric='logloss',
            tree_method='hist'
            # Removed early_stopping_rounds to fix the error
        ),
        'RandomForest_Ultra': RandomForestClassifier(
            n_estimators=600,
            max_depth=25,
            min_samples_split=2,
            min_samples_leaf=1,
            max_features='sqrt',
            bootstrap=True,
            oob_score=True,
            random_state=42,
            n_jobs=-1
        ),
        'GradientBoosting_Ultra': GradientBoostingClassifier(
            n_estimators=400,
            learning_rate=0.05,
            max_depth=8,
            min_samples_split=2,
            min_samples_leaf=1,
            subsample=0.8,
            max_features='sqrt',
            random_state=42
        )
    }
    return models

def comprehensive_evaluation(y_true, y_pred, y_pred_proba, model_name):
    """Comprehensive evaluation with all metrics"""

    # Basic metrics
    accuracy = accuracy_score(y_true, y_pred)
    precision = precision_score(y_true, y_pred, zero_division=0)
    recall = recall_score(y_true, y_pred, zero_division=0)
    f1 = f1_score(y_true, y_pred, zero_division=0)

    # Advanced metrics
    balanced_acc = balanced_accuracy_score(y_true, y_pred)
    matthews_cc = matthews_corrcoef(y_true, y_pred)
    cohen_kappa = cohen_kappa_score(y_true, y_pred)

    # ROC and PR AUC
    if y_pred_proba is not None:
        roc_auc = roc_auc_score(y_true, y_pred_proba)
        precision_curve, recall_curve, _ = precision_recall_curve(y_true, y_pred_proba)
        pr_auc = np.trapz(precision_curve, recall_curve)
    else:
        roc_auc = 0.0
        pr_auc = 0.0

    # Confusion matrix
    cm = confusion_matrix(y_true, y_pred)
    tn, fp, fn, tp = cm.ravel()

    # Specificity and sensitivity
    specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
    sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0

    # False positive and negative rates
    fpr = fp / (fp + tn) if (fp + tn) > 0 else 0
    fnr = fn / (fn + tp) if (fn + tp) > 0 else 0

    # Positive and negative predictive values
    ppv = tp / (tp + fp) if (tp + fp) > 0 else 0  # Same as precision
    npv = tn / (tn + fn) if (tn + fn) > 0 else 0

    # Likelihood ratios
    lr_positive = sensitivity / fpr if fpr > 0 else float('inf')
    lr_negative = fnr / specificity if specificity > 0 else float('inf')

    # Diagnostic odds ratio
    dor = lr_positive / lr_negative if lr_negative > 0 else float('inf')

    metrics = {
        'accuracy': accuracy,
        'balanced_accuracy': balanced_acc,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'specificity': specificity,
        'sensitivity': sensitivity,
        'roc_auc': roc_auc,
        'pr_auc': pr_auc,
        'matthews_cc': matthews_cc,
        'cohen_kappa': cohen_kappa,
        'fpr': fpr,
        'fnr': fnr,
        'ppv': ppv,
        'npv': npv,
        'lr_positive': lr_positive,
        'lr_negative': lr_negative,
        'dor': dor,
        'true_positives': int(tp),
        'true_negatives': int(tn),
        'false_positives': int(fp),
        'false_negatives': int(fn)
    }

    return metrics, cm

def plot_comprehensive_results(results, y_test, y_pred_dict, y_proba_dict, save_dir):
    """Create comprehensive visualization plots"""

    # 1. Performance comparison
    plt.figure(figsize=(15, 12))

    # Accuracy comparison
    plt.subplot(2, 3, 1)
    models = list(results.keys())
    accuracies = [results[model]['accuracy'] for model in models]
    colors = plt.cm.Set2(np.linspace(0, 1, len(models)))

    bars = plt.bar(range(len(models)), accuracies, color=colors, alpha=0.8)
    plt.axhline(y=0.94, color='red', linestyle='--', linewidth=2, label='Target: 94%')
    plt.axhline(y=0.97, color='green', linestyle='--', linewidth=2, label='Stretch: 97%')
    plt.xlabel('Models')
    plt.ylabel('Accuracy')
    plt.title('Accuracy Comparison')
    plt.xticks(range(len(models)), models, rotation=45)
    plt.legend()
    plt.grid(True, alpha=0.3)

    for bar, acc in zip(bars, accuracies):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                f'{acc:.3f}', ha='center', va='bottom', fontweight='bold')

    # F1-Score comparison
    plt.subplot(2, 3, 2)
    f1_scores = [results[model]['f1_score'] for model in models]
    plt.bar(range(len(models)), f1_scores, color=colors, alpha=0.8)
    plt.xlabel('Models')
    plt.ylabel('F1-Score')
    plt.title('F1-Score Comparison')
    plt.xticks(range(len(models)), models, rotation=45)
    plt.grid(True, alpha=0.3)

    # ROC AUC comparison
    plt.subplot(2, 3, 3)
    roc_aucs = [results[model]['roc_auc'] for model in models]
    plt.bar(range(len(models)), roc_aucs, color=colors, alpha=0.8)
    plt.xlabel('Models')
    plt.ylabel('ROC AUC')
    plt.title('ROC AUC Comparison')
    plt.xticks(range(len(models)), models, rotation=45)
    plt.grid(True, alpha=0.3)

    # Matthews Correlation Coefficient
    plt.subplot(2, 3, 4)
    mcc_scores = [results[model]['matthews_cc'] for model in models]
    plt.bar(range(len(models)), mcc_scores, color=colors, alpha=0.8)
    plt.xlabel('Models')
    plt.ylabel('Matthews CC')
    plt.title('Matthews Correlation Coefficient')
    plt.xticks(range(len(models)), models, rotation=45)
    plt.grid(True, alpha=0.3)

    # Balanced Accuracy
    plt.subplot(2, 3, 5)
    bal_accs = [results[model]['balanced_accuracy'] for model in models]
    plt.bar(range(len(models)), bal_accs, color=colors, alpha=0.8)
    plt.xlabel('Models')
    plt.ylabel('Balanced Accuracy')
    plt.title('Balanced Accuracy Comparison')
    plt.xticks(range(len(models)), models, rotation=45)
    plt.grid(True, alpha=0.3)

    # Cohen's Kappa
    plt.subplot(2, 3, 6)
    kappa_scores = [results[model]['cohen_kappa'] for model in models]
    plt.bar(range(len(models)), kappa_scores, color=colors, alpha=0.8)
    plt.xlabel('Models')
    plt.ylabel("Cohen's Kappa")
    plt.title("Cohen's Kappa Comparison")
    plt.xticks(range(len(models)), models, rotation=45)
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(f'{save_dir}/comprehensive_performance.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 2. Confusion matrices for all models
    n_models = len(models)
    fig, axes = plt.subplots(1, n_models, figsize=(6*n_models, 5))
    if n_models == 1:
        axes = [axes]

    for i, model in enumerate(models):
        cm = confusion_matrix(y_test, y_pred_dict[model])
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[i],
                   xticklabels=['Unhealthy', 'Healthy'],
                   yticklabels=['Unhealthy', 'Healthy'])
        axes[i].set_title(f'{model}\nAcc: {results[model]["accuracy"]:.3f}')
        axes[i].set_ylabel('True Label')
        axes[i].set_xlabel('Predicted Label')

    plt.tight_layout()
    plt.savefig(f'{save_dir}/confusion_matrices.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 3. ROC Curves
    plt.figure(figsize=(10, 8))
    for model in models:
        if model in y_proba_dict:
            fpr, tpr, _ = roc_curve(y_test, y_proba_dict[model])
            plt.plot(fpr, tpr, label=f'{model} (AUC = {results[model]["roc_auc"]:.3f})', linewidth=2)

    plt.plot([0, 1], [0, 1], 'k--', label='Random Classifier')
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title('ROC Curves Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig(f'{save_dir}/roc_curves.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 4. Precision-Recall Curves
    plt.figure(figsize=(10, 8))
    for model in models:
        if model in y_proba_dict:
            precision_curve, recall_curve, _ = precision_recall_curve(y_test, y_proba_dict[model])
            plt.plot(recall_curve, precision_curve, label=f'{model} (AUC = {results[model]["pr_auc"]:.3f})', linewidth=2)

    plt.xlabel('Recall')
    plt.ylabel('Precision')
    plt.title('Precision-Recall Curves Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig(f'{save_dir}/precision_recall_curves.png', dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """Main pipeline optimized for 94-97% accuracy with comprehensive evaluation"""
    print("🎯" + "="*80)
    print("OPTIMIZED HIGH-ACCURACY BRIDGE MONITORING (TARGET: 94-97%)")
    print("Comprehensive Evaluation with All Metrics")
    print("🎯" + "="*80)

    start_time = time.time()
    base_path = "/Users/<USER>/Documents/bridge_sensor/data/"
    save_dir = "optimized_results"
    os.makedirs(save_dir, exist_ok=True)

    # Load data with strict quality control
    print("\n1. LOADING DATA WITH STRICT QUALITY CONTROL")
    print("-" * 50)
    healthy_data, unhealthy_data = load_optimized_data(base_path)

    if len(healthy_data) < 10 or len(unhealthy_data) < 10:
        print("❌ Insufficient high-quality data")
        return

    # Extract comprehensive features
    print("\n2. COMPREHENSIVE FEATURE EXTRACTION")
    print("-" * 40)

    all_features = []
    all_labels = []
    file_ids = []

    # Process healthy files
    for file_idx, df in enumerate(healthy_data):
        print(f"🟢 Processing healthy file {file_idx+1}/{len(healthy_data)}")
        features_list = extract_optimized_features(df)
        for features in features_list:
            all_features.append(features)
            all_labels.append(1)
            file_ids.append(f"healthy_{file_idx}")

    # Process unhealthy files
    for file_idx, df in enumerate(unhealthy_data):
        print(f"🔴 Processing unhealthy file {file_idx+1}/{len(unhealthy_data)}")
        features_list = extract_optimized_features(df)
        for features in features_list:
            all_features.append(features)
            all_labels.append(0)
            file_ids.append(f"unhealthy_{file_idx}")

    # Convert to DataFrame
    X = pd.DataFrame(all_features)
    y = np.array(all_labels)
    file_ids = np.array(file_ids)

    print(f"\n✅ Comprehensive feature extraction completed:")
    print(f"📊 Total windows: {len(X)}")
    print(f"📊 Total features: {X.shape[1]}")
    print(f"🎯 Healthy windows: {sum(y==1)}")
    print(f"🎯 Unhealthy windows: {sum(y==0)}")

    # Advanced preprocessing
    print("\n3. ADVANCED PREPROCESSING")
    print("-" * 30)

    # Handle missing values robustly
    missing_threshold = 0.02  # Very strict
    X_clean = X.loc[:, X.isnull().mean() < missing_threshold]
    print(f"📊 Features after removing high-missing: {X_clean.shape[1]}")

    # Advanced imputation
    X_clean = X_clean.fillna(X_clean.median())
    X_clean = X_clean.replace([np.inf, -np.inf], np.nan)

    for col in X_clean.columns:
        if X_clean[col].isna().any():
            median_val = X_clean[col].median()
            if pd.isna(median_val):
                X_clean[col] = X_clean[col].fillna(0)
            else:
                X_clean[col] = X_clean[col].fillna(median_val)

    # Remove low-variance features
    from sklearn.feature_selection import VarianceThreshold
    variance_selector = VarianceThreshold(threshold=1e-6)
    X_clean = pd.DataFrame(variance_selector.fit_transform(X_clean),
                          columns=X_clean.columns[variance_selector.get_support()])
    print(f"📊 Features after variance filtering: {X_clean.shape[1]}")

    # Advanced feature selection
    print("\n4. ADVANCED FEATURE SELECTION")
    print("-" * 35)

    # Multiple feature selection methods
    n_features = min(100, X_clean.shape[1])  # More features for higher accuracy

    # Method 1: Mutual Information
    selector_mi = SelectKBest(score_func=mutual_info_classif, k=n_features)
    X_mi = selector_mi.fit_transform(X_clean, y)

    # Method 2: F-test
    selector_f = SelectKBest(score_func=f_classif, k=n_features)
    X_f = selector_f.fit_transform(X_clean, y)

    # Method 3: Random Forest feature importance
    rf_selector = RandomForestClassifier(n_estimators=100, random_state=42)
    rf_selector.fit(X_clean, y)
    feature_importance = rf_selector.feature_importances_
    top_features_rf = np.argsort(feature_importance)[-n_features:]

    # Combine feature selections (union)
    selected_features_mi = set(X_clean.columns[selector_mi.get_support()])
    selected_features_f = set(X_clean.columns[selector_f.get_support()])
    selected_features_rf = set(X_clean.columns[top_features_rf])

    # Take union of all methods
    all_selected_features = selected_features_mi.union(selected_features_f).union(selected_features_rf)
    X_selected = X_clean[list(all_selected_features)]

    print(f"✅ Selected {X_selected.shape[1]} features using ensemble selection")

    # File-based train-test split
    print("\n5. STRATIFIED FILE-BASED SPLIT")
    print("-" * 35)

    unique_files = np.unique(file_ids)
    healthy_files = [f for f in unique_files if f.startswith('healthy')]
    unhealthy_files = [f for f in unique_files if f.startswith('unhealthy')]

    # Stratified split by files
    healthy_train_files, healthy_test_files = train_test_split(
        healthy_files, test_size=0.15, random_state=42
    )
    unhealthy_train_files, unhealthy_test_files = train_test_split(
        unhealthy_files, test_size=0.15, random_state=42
    )

    train_files = healthy_train_files + unhealthy_train_files
    test_files = healthy_test_files + unhealthy_test_files

    train_mask = np.isin(file_ids, train_files)
    test_mask = np.isin(file_ids, test_files)

    X_train = X_selected.values[train_mask]
    X_test = X_selected.values[test_mask]
    y_train = y[train_mask]
    y_test = y[test_mask]

    print(f"📈 Training windows: {X_train.shape}")
    print(f"📉 Test windows: {X_test.shape}")

    # Advanced class balancing
    print("\n6. ADVANCED CLASS BALANCING")
    print("-" * 35)

    # Try multiple balancing techniques
    balancing_methods = {
        'SMOTE': SMOTE(random_state=42, k_neighbors=3),
        'BorderlineSMOTE': BorderlineSMOTE(random_state=42, k_neighbors=3),
        'SMOTEENN': SMOTEENN(random_state=42)
    }

    best_balance_method = None
    best_balance_score = 0

    for method_name, method in balancing_methods.items():
        try:
            X_temp, y_temp = method.fit_resample(X_train, y_train)
            # Quick evaluation
            temp_rf = RandomForestClassifier(n_estimators=50, random_state=42)
            temp_scores = cross_val_score(temp_rf, X_temp, y_temp, cv=3, scoring='f1')
            avg_score = temp_scores.mean()
            print(f"📊 {method_name}: F1 = {avg_score:.3f}")

            if avg_score > best_balance_score:
                best_balance_score = avg_score
                best_balance_method = method
                X_train_balanced = X_temp
                y_train_balanced = y_temp
        except Exception as e:
            print(f"❌ {method_name} failed: {e}")

    if best_balance_method is None:
        print("⚠️ Using original unbalanced data")
        X_train_balanced = X_train
        y_train_balanced = y_train
    else:
        print(f"✅ Best balancing method selected with F1 = {best_balance_score:.3f}")

    print(f"📊 After balancing: {X_train_balanced.shape}")
    print(f"🎯 Balanced classes: Healthy={sum(y_train_balanced==1)}, Unhealthy={sum(y_train_balanced==0)}")

    # Advanced scaling
    scaler = PowerTransformer(method='yeo-johnson', standardize=True)
    X_train_scaled = scaler.fit_transform(X_train_balanced)
    X_test_scaled = scaler.transform(X_test)

    # Train optimized models
    print("\n7. TRAINING OPTIMIZED MODELS")
    print("-" * 35)

    models = create_optimized_models()
    results = {}
    y_pred_dict = {}
    y_proba_dict = {}

    for name, model in models.items():
        print(f"🔥 Training {name}...")
        start_model_time = time.time()

        try:
            # Cross-validation
            cv_scores = cross_val_score(model, X_train_scaled, y_train_balanced, cv=5, scoring='accuracy')

            # Train on full training set
            model.fit(X_train_scaled, y_train_balanced)

            # Predict on test set
            y_pred = model.predict(X_test_scaled)
            y_pred_proba = model.predict_proba(X_test_scaled)[:, 1] if hasattr(model, 'predict_proba') else None

            # Store predictions
            y_pred_dict[name] = y_pred
            if y_pred_proba is not None:
                y_proba_dict[name] = y_pred_proba

            # Comprehensive evaluation
            metrics, cm = comprehensive_evaluation(y_test, y_pred, y_pred_proba, name)
            metrics['cv_mean'] = cv_scores.mean()
            metrics['cv_std'] = cv_scores.std()
            metrics['training_time'] = time.time() - start_model_time

            results[name] = metrics

            print(f"✅ {name}: Acc={metrics['accuracy']:.4f}, F1={metrics['f1_score']:.4f}, AUC={metrics['roc_auc']:.4f}")
            print(f"   CV={cv_scores.mean():.4f}±{cv_scores.std():.4f}, MCC={metrics['matthews_cc']:.4f}")

        except Exception as e:
            print(f"❌ {name} failed: {e}")

    # Results analysis
    total_time = time.time() - start_time

    if results:
        best_model = max(results.keys(), key=lambda k: results[k]['accuracy'])
        best_accuracy = results[best_model]['accuracy']
        best_f1 = results[best_model]['f1_score']
        best_auc = results[best_model]['roc_auc']

        # Save comprehensive results
        import json
        summary = {
            'methodology': 'Optimized ML for 94-97% accuracy with comprehensive evaluation',
            'target_accuracy': '94-97%',
            'achieved_accuracy': f"{best_accuracy:.4f}",
            'total_runtime': f"{total_time:.2f} seconds",
            'files_processed': len(healthy_data) + len(unhealthy_data),
            'windows_created': len(X),
            'features_extracted': X.shape[1],
            'features_selected': X_selected.shape[1],
            'best_model': {
                'name': best_model,
                'accuracy': best_accuracy,
                'f1_score': best_f1,
                'roc_auc': best_auc
            },
            'all_results': results
        }

        with open(f'{save_dir}/optimized_results.json', 'w') as f:
            json.dump(summary, f, indent=2, default=str)

        results_df = pd.DataFrame(results).T
        results_df.to_csv(f'{save_dir}/optimized_performance.csv')

        # Generate comprehensive plots
        plot_comprehensive_results(results, y_test, y_pred_dict, y_proba_dict, save_dir)

        # Detailed classification report for best model
        print(f"\n📊 COMPREHENSIVE EVALUATION FOR BEST MODEL: {best_model}")
        print("-" * 70)
        print(classification_report(y_test, y_pred_dict[best_model], target_names=['Unhealthy', 'Healthy']))

        # Print all metrics for best model
        best_metrics = results[best_model]
        print(f"\n📈 DETAILED METRICS FOR {best_model}:")
        print("-" * 50)
        print(f"Accuracy:           {best_metrics['accuracy']:.4f}")
        print(f"Balanced Accuracy:  {best_metrics['balanced_accuracy']:.4f}")
        print(f"Precision:          {best_metrics['precision']:.4f}")
        print(f"Recall:             {best_metrics['recall']:.4f}")
        print(f"F1-Score:           {best_metrics['f1_score']:.4f}")
        print(f"Specificity:        {best_metrics['specificity']:.4f}")
        print(f"Sensitivity:        {best_metrics['sensitivity']:.4f}")
        print(f"ROC AUC:            {best_metrics['roc_auc']:.4f}")
        print(f"PR AUC:             {best_metrics['pr_auc']:.4f}")
        print(f"Matthews CC:        {best_metrics['matthews_cc']:.4f}")
        print(f"Cohen's Kappa:      {best_metrics['cohen_kappa']:.4f}")
        print(f"False Positive Rate: {best_metrics['fpr']:.4f}")
        print(f"False Negative Rate: {best_metrics['fnr']:.4f}")
        print(f"Positive Predictive Value: {best_metrics['ppv']:.4f}")
        print(f"Negative Predictive Value: {best_metrics['npv']:.4f}")
        print(f"Likelihood Ratio (+): {best_metrics['lr_positive']:.4f}")
        print(f"Likelihood Ratio (-): {best_metrics['lr_negative']:.4f}")
        print(f"Diagnostic Odds Ratio: {best_metrics['dor']:.4f}")

        print(f"\n🔢 CONFUSION MATRIX FOR {best_model}:")
        print("-" * 40)
        cm = confusion_matrix(y_test, y_pred_dict[best_model])
        print(f"True Negatives:  {best_metrics['true_negatives']}")
        print(f"False Positives: {best_metrics['false_positives']}")
        print(f"False Negatives: {best_metrics['false_negatives']}")
        print(f"True Positives:  {best_metrics['true_positives']}")

    print(f"\n🎯" + "="*80)
    print("OPTIMIZED HIGH-ACCURACY RESULTS COMPLETED! ✅")
    print(f"⏱️  Total runtime: {total_time:.2f} seconds")
    print(f"📁 Files processed: {len(healthy_data)} healthy + {len(unhealthy_data)} unhealthy")
    print(f"🏆 Best model: {best_model}")
    print(f"📈 Best Accuracy: {best_accuracy:.4f} {'🎯 TARGET ACHIEVED!' if best_accuracy >= 0.94 else '❌ Target not reached'}")
    print(f"📈 Best F1 Score: {best_f1:.4f}")
    print(f"📈 Best ROC AUC: {best_auc:.4f}")
    print(f"💾 Results saved to {save_dir}/ directory")
    print("\n🔍 COMPREHENSIVE EVALUATION INCLUDES:")
    print("• Accuracy, Balanced Accuracy, Precision, Recall, F1-Score")
    print("• Specificity, Sensitivity, ROC AUC, PR AUC")
    print("• Matthews Correlation Coefficient, Cohen's Kappa")
    print("• False Positive/Negative Rates, Predictive Values")
    print("• Likelihood Ratios, Diagnostic Odds Ratio")
    print("• Detailed Confusion Matrix Analysis")
    print("• ROC and Precision-Recall Curves")
    print("• Cross-validation with confidence intervals")
    print("🎯" + "="*80)

if __name__ == "__main__":
    main()
